/* BASICS */

.CodeMirror {
	/* Set height, width, borders, and global font properties here */
	width: 100%;
	height: 100%;
	color: black;
}
.CodeMirror div {
	display: block;
	text-shadow: none;
	transition: all 0s;
	position: static;
}

/* PADDING */

.CodeMirror-lines {
	padding: 4px 0; /* Vertical padding around content */
}
.CodeMirror pre {
	padding: 0 4px; /* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler,
.CodeMirror-gutter-filler {
	background-color: white; /* The little square between H and V scrollbars */
}

/* GUTTER */

.CodeMirror-gutters {
	border-right: 1px solid #ddd;
	background-color: #f7f7f7;
	white-space: nowrap;
}
.CodeMirror-linenumbers {
}
.CodeMirror-linenumber {
	padding: 0 3px 0 5px;
	min-width: 20px;
	text-align: right;
	color: #999;
	white-space: nowrap;
}

.CodeMirror-guttermarker {
	color: black;
}
.CodeMirror-guttermarker-subtle {
	color: #999;
}

/* CURSOR */

.CodeMirror-cursor {
	border-left: 1px solid black;
	border-right: none;
	width: 0;
}
/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
	border-left: 1px solid silver;
}
.cm-fat-cursor .CodeMirror-cursor {
	width: auto;
	border: 0 !important;
	background: #7e7;
}
.cm-fat-cursor div.CodeMirror-cursors {
	z-index: 1;
}

.cm-animate-fat-cursor {
	width: auto;
	border: 0;
	-webkit-animation: blink 1.06s steps(1) infinite;
	-moz-animation: blink 1.06s steps(1) infinite;
	animation: blink 1.06s steps(1) infinite;
	background-color: #7e7;
}
@-moz-keyframes blink {
	0% {
	}
	50% {
		background-color: transparent;
	}
	100% {
	}
}
@-webkit-keyframes blink {
	0% {
	}
	50% {
		background-color: transparent;
	}
	100% {
	}
}
@keyframes blink {
	0% {
	}
	50% {
		background-color: transparent;
	}
	100% {
	}
}

/* Can style cursor different in overwrite (non-insert) mode */
.CodeMirror-overwrite .CodeMirror-cursor {
}

.cm-tab {
	display: inline-block;
	text-decoration: inherit;
}

.CodeMirror .CodeMirror-rulers {
	position: absolute;
	left: 0;
	right: 0;
	top: -50px;
	bottom: -20px;
	overflow: hidden;
}
.CodeMirror .CodeMirror-ruler {
	border-left: 1px solid #ccc;
	top: 0;
	bottom: 0;
	position: absolute;
}

/* DEFAULT THEME */

.cm-s-default .cm-header {
	color: blue;
}
.cm-s-default .cm-quote {
	color: #090;
}
.cm-negative {
	color: #d44;
}
.cm-positive {
	color: #292;
}
.cm-header,
.cm-strong {
	font-weight: bold;
}
.cm-em {
	font-style: italic;
}
.cm-link {
	text-decoration: underline;
}
.cm-strikethrough {
	text-decoration: line-through;
}

.cm-s-default .cm-keyword {
	color: #708;
}
.cm-s-default .cm-atom {
	color: #219;
}
.cm-s-default .cm-number {
	color: #164;
}
.cm-s-default .cm-def {
	color: #00f;
}
.cm-s-default .cm-variable,
.cm-s-default .cm-punctuation,
.cm-s-default .cm-property,
.cm-s-default .cm-operator {
}
.cm-s-default .cm-variable-2 {
	color: #05a;
}
.cm-s-default .cm-variable-3 {
	color: #085;
}
.cm-s-default .cm-comment {
	color: #a50;
}
.cm-s-default .cm-string {
	color: #a11;
}
.cm-s-default .cm-string-2 {
	color: #f50;
}
.cm-s-default .cm-meta {
	color: #555;
}
.cm-s-default .cm-qualifier {
	color: #555;
}
.cm-s-default .cm-builtin {
	color: #30a;
}
.cm-s-default .cm-bracket {
	color: #997;
}
.cm-s-default .cm-tag {
	color: #170;
}
.cm-s-default .cm-attribute {
	color: #00c;
}
.cm-s-default .cm-hr {
	color: #999;
}
.cm-s-default .cm-link {
	color: #00c;
}

.cm-s-default .cm-error {
	color: #f00;
}
.cm-invalidchar {
	color: #f00;
}

.CodeMirror-composing {
	border-bottom: 2px solid;
}

/* Default styles for common addons */

div.CodeMirror span.CodeMirror-matchingbracket {
	color: #0f0;
}
div.CodeMirror span.CodeMirror-nonmatchingbracket {
	color: #f22;
}
.CodeMirror-matchingtag {
	background: rgba(255, 150, 0, 0.3);
}
.CodeMirror-activeline-background {
	background: #e8f2ff;
}

/* STOP */

/* The rest of this file contains styles related to the mechanics of
   the editor. You probably shouldn't touch them. */

.CodeMirror {
	position: relative;
	overflow: hidden;
	background: white;
}

.CodeMirror .CodeMirror-scroll {
	overflow: scroll !important; /* Things will break if this is overridden */
	/* 30px is the magic margin used to hide the element's real scrollbars */
	/* See overflow: hidden in .CodeMirror */
	margin-bottom: -30px;
	margin-right: -30px;
	padding-bottom: 30px;
	height: 100%;
	outline: none; /* Prevent dragging from highlighting the element */
	position: relative;
}
.CodeMirror .CodeMirror-sizer {
	position: relative;
	border-right: 30px solid transparent;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
   before actual scrolling happens, thus preventing shaking and
   flickering artifacts. */
.CodeMirror .CodeMirror-vscrollbar,
.CodeMirror .CodeMirror-hscrollbar,
.CodeMirror .CodeMirror-scrollbar-filler,
.CodeMirror .CodeMirror-gutter-filler {
	position: absolute;
	z-index: 6;
	display: none;
}
.CodeMirror-vscrollbar {
	right: 0;
	top: 0;
	overflow-x: hidden;
	overflow-y: scroll;
}
.CodeMirror-hscrollbar {
	bottom: 0;
	left: 0;
	overflow-y: hidden;
	overflow-x: scroll;
}
.CodeMirror-scrollbar-filler {
	right: 0;
	bottom: 0;
}
.CodeMirror-gutter-filler {
	left: 0;
	bottom: 0;
}

.CodeMirror .CodeMirror-gutters {
	position: absolute;
	left: 0;
	top: 0;
	min-height: 100%;
	z-index: 3;
}
.CodeMirror-gutter {
	white-space: normal;
	height: 100%;
	display: inline-block;
	vertical-align: top;
	margin-bottom: -30px;
	/* Hack to make IE7 behave */
	/* *zoom: 1;
	*display: inline; */
}
.CodeMirror .CodeMirror-gutter-wrapper {
	position: absolute;
	z-index: 4;
	background: none !important;
	border: none !important;
}
.CodeMirror .CodeMirror-gutter-background {
	position: absolute;
	top: 0;
	bottom: 0;
	z-index: 4;
}
.CodeMirror .CodeMirror-gutter-elt {
	position: absolute;
	cursor: default;
	z-index: 4;
}
.CodeMirror-gutter-wrapper {
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;
}

.CodeMirror-lines {
	cursor: text;
	min-height: 1px; /* prevents collapsing before first draw */
}
.CodeMirror pre {
	/* Reset some styles that the rest of the page might have set */
	-moz-border-radius: 0;
	-webkit-border-radius: 0;
	border-radius: 0;
	border-width: 0;
	background: transparent;
	/*font-family: inherit;*/
	/*font-family:'STHeiti','SimHei','Microsoft JhengHei','Microsoft YaHei','WenQuanYi Micro Hei',Helvetica,Arial,sans-serif;*/
	font-size: inherit;
	margin: 0;
	white-space: pre;
	word-wrap: normal;
	line-height: inherit;
	color: inherit;
	z-index: 2;
	position: relative;
	overflow: visible;
	-webkit-tap-highlight-color: transparent;
	-webkit-font-variant-ligatures: none;
	font-variant-ligatures: none;
}
.CodeMirror-wrap pre {
	word-wrap: break-word;
	white-space: pre-wrap;
	word-break: normal;
}

.CodeMirror .CodeMirror-linebackground {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 0;
}

.CodeMirror .CodeMirror-linewidget {
	position: relative;
	z-index: 2;
	overflow: auto;
}

.CodeMirror-widget {
}

.CodeMirror-code {
	outline: none;
}

/* Force content-box sizing for the elements where we expect it */
.CodeMirror-scroll,
.CodeMirror-sizer,
.CodeMirror-gutter,
.CodeMirror-gutters,
.CodeMirror-linenumber {
	-moz-box-sizing: content-box;
	box-sizing: content-box;
}

.CodeMirror .CodeMirror-measure {
	position: absolute;
	width: 100%;
	height: 0;
	overflow: hidden;
	visibility: hidden;
}

.CodeMirror .CodeMirror-cursor {
	position: absolute;
	pointer-events: none;
}
.CodeMirror-measure pre {
	position: static;
}

.CodeMirror div.CodeMirror-cursors {
	visibility: hidden;
	position: relative;
	z-index: 3;
}
div.CodeMirror-dragcursors {
	visibility: visible;
}

.CodeMirror-focused div.CodeMirror-cursors {
	visibility: visible;
}

.CodeMirror-selected {
	background: #d9d9d9;
}
.CodeMirror-focused .CodeMirror-selected {
	background: #d7d4f0;
}
.CodeMirror-crosshair {
	cursor: crosshair;
}
.CodeMirror-line::selection,
.CodeMirror-line > span::selection,
.CodeMirror-line > span > span::selection {
	background: #d7d4f0;
}
.CodeMirror-line::-moz-selection,
.CodeMirror-line > span::-moz-selection,
.CodeMirror-line > span > span::-moz-selection {
	background: #d7d4f0;
}

.cm-searching {
	background: #ffa;
	background: rgba(255, 255, 0, 0.4);
}

/* IE7 hack to prevent it from returning funny offsetTops on the spans */
.CodeMirror span {
	*vertical-align: text-bottom;
}

/* Used to force a border model for a node */
.cm-force-border {
	padding-right: 0.1px;
}

@media print {
	/* Hide the cursor when printing */
	.CodeMirror div.CodeMirror-cursors {
		visibility: hidden;
	}
}

/* See issue #2901 */
.cm-tab-wrap-hack:after {
	content: "";
}

/* Help users use markselection to safely style text background */
span.CodeMirror-selectedtext {
	background: none;
}

/*
  MDN-LIKE Theme - Mozilla
  Ported to CodeMirror by Peter Kroon <<EMAIL>>
  Report bugs/issues here: https://github.com/codemirror/CodeMirror/issues
  GitHub: @peterkroon

  The mdn-like theme is inspired on the displayed code examples at: https://developer.mozilla.org/en-US/docs/Web/CSS/animation

*/
.cm-s-mdn-like.CodeMirror {
	color: #999;
	background-color: #fff;
}
.cm-s-mdn-like div.CodeMirror-selected {
	background: #cfc;
}
.cm-s-mdn-like .CodeMirror-line::selection,
.cm-s-mdn-like .CodeMirror-line > span::selection,
.cm-s-mdn-like .CodeMirror-line > span > span::selection {
	background: #cfc;
}
.cm-s-mdn-like .CodeMirror-line::-moz-selection,
.cm-s-mdn-like .CodeMirror-line > span::-moz-selection,
.cm-s-mdn-like .CodeMirror-line > span > span::-moz-selection {
	background: #cfc;
}

.cm-s-mdn-like .CodeMirror-gutters {
	background: #f8f8f8;
	border-left: 6px solid rgba(0, 83, 159, 0);
	color: #333;
}
.cm-s-mdn-like .CodeMirror-linenumber {
	color: #aaa;
	padding-left: 8px;
}
.cm-s-mdn-like .CodeMirror-cursor {
	border-left: 2px solid #222;
}

.cm-s-mdn-like .cm-keyword {
	color: #6262ff;
}
.cm-s-mdn-like .cm-atom {
	color: #f90;
}
.cm-s-mdn-like .cm-number {
	color: #ca7841;
}
.cm-s-mdn-like .cm-def {
	color: #8da6ce;
}
.cm-s-mdn-like span.cm-variable-2,
.cm-s-mdn-like span.cm-tag {
	color: #690;
}
.cm-s-mdn-like span.cm-variable-3,
.cm-s-mdn-like span.cm-def {
	color: #07a;
}

.cm-s-mdn-like .cm-variable {
	color: #07a;
}
.cm-s-mdn-like .cm-property {
	color: #905;
}
.cm-s-mdn-like .cm-qualifier {
	color: #690;
}

.cm-s-mdn-like .cm-operator {
	color: #cda869;
}
.cm-s-mdn-like .cm-comment {
	color: #777;
	font-weight: normal;
}
.cm-s-mdn-like .cm-string {
	color: #07a;
	font-style: italic;
}
.cm-s-mdn-like .cm-string-2 {
	color: #bd6b18;
} /*?*/
.cm-s-mdn-like .cm-meta {
	color: #000;
} /*?*/
.cm-s-mdn-like .cm-builtin {
	color: #9b7536;
} /*?*/
.cm-s-mdn-like .cm-tag {
	color: #997643;
}
.cm-s-mdn-like .cm-attribute {
	color: #d6bb6d;
} /*?*/
.cm-s-mdn-like .cm-header {
	color: #ff6400;
}
.cm-s-mdn-like .cm-hr {
	color: #aeaeae;
}
.cm-s-mdn-like .cm-link {
	color: #ad9361;
	font-style: italic;
	text-decoration: none;
}
.cm-s-mdn-like .cm-error {
	border-bottom: 1px solid red;
}

div.cm-s-mdn-like .CodeMirror-activeline-background {
	background: #efefff;
}
div.cm-s-mdn-like span.CodeMirror-matchingbracket {
	outline: 1px solid grey;
	color: inherit;
}

/*.cm-s-mdn-like.CodeMirror { background-image: url(data:image/png;base64,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); }*/

/****************************************************************/
/*   Based on mbonaci's Brackets mbo theme                      */
/*   https://github.com/mbonaci/global/blob/master/Mbo.tmTheme  */
/*   Create your own: http://tmtheme-editor.herokuapp.com       */
/****************************************************************/

.cm-s-mbo.CodeMirror {
	background: #2c2c2c;
	color: #ffffec;
}
.cm-s-mbo div.CodeMirror-selected {
	background: #716c62;
}
.cm-s-mbo .CodeMirror-line::selection,
.cm-s-mbo .CodeMirror-line > span::selection,
.cm-s-mbo .CodeMirror-line > span > span::selection {
	background: rgba(113, 108, 98, 0.99);
}
.cm-s-mbo .CodeMirror-line::-moz-selection,
.cm-s-mbo .CodeMirror-line > span::-moz-selection,
.cm-s-mbo .CodeMirror-line > span > span::-moz-selection {
	background: rgba(113, 108, 98, 0.99);
}
.cm-s-mbo .CodeMirror-gutters {
	background: #4e4e4e;
	border-right: 0px;
}
.cm-s-mbo .CodeMirror-guttermarker {
	color: white;
}
.cm-s-mbo .CodeMirror-guttermarker-subtle {
	color: grey;
}
.cm-s-mbo .CodeMirror-linenumber {
	color: #dadada;
}
.cm-s-mbo .CodeMirror-cursor {
	border-left: 1px solid #ffffec;
}

.cm-s-mbo span.cm-comment {
	color: #95958a;
}
.cm-s-mbo span.cm-atom {
	color: #00a8c6;
}
.cm-s-mbo span.cm-number {
	color: #00a8c6;
}

.cm-s-mbo span.cm-property,
.cm-s-mbo span.cm-attribute {
	color: #9ddfe9;
}
.cm-s-mbo span.cm-keyword {
	color: #ffb928;
}
.cm-s-mbo span.cm-string {
	color: #ffcf6c;
}
.cm-s-mbo span.cm-string.cm-property {
	color: #ffffec;
}

.cm-s-mbo span.cm-variable {
	color: #ffffec;
}
.cm-s-mbo span.cm-variable-2 {
	color: #00a8c6;
}
.cm-s-mbo span.cm-def {
	color: #ffffec;
}
.cm-s-mbo span.cm-bracket {
	color: #fffffc;
	font-weight: bold;
}
.cm-s-mbo span.cm-tag {
	color: #9ddfe9;
}
.cm-s-mbo span.cm-link {
	color: #f54b07;
}
.cm-s-mbo span.cm-error {
	border-bottom: #636363;
	color: #ffffec;
}
.cm-s-mbo span.cm-qualifier {
	color: #ffffec;
}

.cm-s-mbo .CodeMirror-activeline-background {
	background: #494b41;
}
.cm-s-mbo .CodeMirror-matchingbracket {
	color: #ffb928 !important;
}
.cm-s-mbo .CodeMirror-matchingtag {
	background: rgba(255, 255, 255, 0.37);
}

.CodeMirror-hints {
	position: absolute;
	z-index: 10;
	overflow: hidden;
	list-style: none;

	margin: 0;
	padding: 2px;

	-webkit-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
	box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
	border-radius: 3px;
	border: 1px solid silver;

	background: white;
	font-size: 90%;
	font-family: monospace;
	text-shadow: none;

	max-height: 20em;
	overflow-y: auto;
}

.CodeMirror-hints::-webkit-scrollbar {
	width: 15px;
	display: block;
}

.CodeMirror-hints::-webkit-scrollbar-thumb {
	background-color: rgb(218, 215, 215);
	border-radius: 5px;
	height: 50px;
}

.CodeMirror-hints::-webkit-scrollbar-track {
	background-color: #6b6565;
}

.CodeMirror-hint {
	margin: 0;
	padding: 0 4px;
	border-radius: 2px;
	white-space: pre;
	color: black;
	cursor: pointer;
}

li.CodeMirror-hint-active {
	background: #08f;
	color: white;
}

.cm-completionIcon {
	position: relative;
	font-size: 90%;
	font-family: monospace;
	width: 0.8em;
	display: inline-block;
	text-align: center;
	padding-right: 0.6em;
	opacity: 0.6;
	box-sizing: content-box;
}

.cm-completionIcon-function:after,
.cm-completionIcon-method:after {
	content: "ƒ";
}
.cm-completionIcon-class:after {
	content: "○";
}
.cm-completionIcon-interface:after {
	content: "◌";
}
.cm-completionIcon-variable:after {
	content: "𝑥";
}
.cm-completionIcon-constant:after {
	content: "𝐶";
}
.cm-completionIcon-type:after {
	content: "𝑡";
}
.cm-completionIcon-enum:after {
	content: "∪";
}
.cm-completionIcon-property:after {
	content: "□";
}
.cm-completionIcon-keyword:after {
	content: "🔑︎";
}
.cm-completionIcon-namespace:after {
	content: "▢";
}
.cm-completionIcon-text:after {
	content: "abc";
	font-size: 50%;
	vertical-align: middle;
}
