@import "../newlayout/layout.css";
#arena {
	height: calc(95% + 20px);
}
#control {
	width: calc(5000% / 47 - 240px);
	left: calc(-150% / 47 + 120px);
	bottom: 150px;
	height: 40px;
}
#arena.phone #control {
	bottom: 160px;
}
#arena.ipad #control {
	bottom: 155px;
}
#arena:not(.chess) > #me,
#arena:not(.chess) > #mebg,
#arena:not(.chess) > #autonode {
	bottom: 30px;
	width: calc(5000% / 47);
	left: calc(-150% / 47);
	top: auto;
	border-radius: 0 !important;
	height: 120px;
}
#arena.oblongcard:not(.chess) > #me,
#arena.oblongcard:not(.chess) > #mebg,
#arena.oblongcard:not(.chess) > #autonode {
	height: 140px;
}
#arena.oblongcard:not(.chess) > .card,
#arena.oblongcard:not(.chess) .handcards > .card {
	height: 120px;
}
#arena.oblongcard:not(.chess) > .card > .image,
#arena.oblongcard:not(.chess) .handcards > .card > .image {
	height: 110px;
	top: 8px;
	background-position-x: -3px;
}
#arena.oblongcard:not(.chess) #handcards1 {
	height: 100%;
	top: 2px;
}
#arena.oblongcard:not(.chess):not(.choose-character) #control {
	bottom: 165px;
}
#arena.phone.oblongcard:not(.chess):not(.choose-character) #control {
	bottom: 180px;
}

#arena:not(.chess) > #autonode {
	width: calc(5000% / 47 - 240px);
	left: calc(-150% / 47 + 120px);
}
#arena:not(.mobile).single-handcard #handcards1 {
	width: calc(100% - 120px);
}
#window.rightbar #system,
#window.leftbar #system {
	width: calc(100% - 62px);
}
#window.leftbar #system {
	left: 50px;
}
#window.rightbar #historybar {
	left: calc(100% - 50px);
	border-radius: 0;
	top: 0;
	height: 100%;
}
#window.leftbar #historybar {
	left: 0;
	border-radius: 0;
	top: 0;
	height: 100%;
}

#window.single-handcard #historybar {
	height: calc(100% - 121px);
}
#window.oblongcard.single-handcard #historybar {
	height: calc(100% - 141px);
}

#window.leftbar #arena:not(.chess) > #me,
#window.leftbar #arena:not(.chess) > #mebg,
#window.leftbar #arena:not(.chess) > #autonode {
	width: calc(5000% / 47 + 2500px / 47);
	left: calc(-150% / 47 - 50px - 75px / 47);
}
#window.leftbar #arena:not(.chess) > #autonode {
	width: calc(5000% / 47 + 2500px / 47 - 240px);
	left: calc(-150% / 47 - 50px - 75px / 47 + 120px);
}
#window.rightbar #arena:not(.chess) > #me,
#window.rightbar #arena:not(.chess) > #mebg,
#window.rightbar #arena:not(.chess) > #autonode {
	width: calc(5000% / 47 + 2500px / 47);
	left: calc(-150% / 47 - 75px / 47);
}
#window.rightbar #arena:not(.chess) > #autonode {
	width: calc(5000% / 47 + 2500px / 47 - 240px);
	left: calc(-150% / 47 - 75px / 47 + 120px);
}
#arena:not(.chess) #handcards1 {
	height: 120px;
	padding: 0;
	top: calc(100% - 120px);
}
#arena:not(.chess) #handcards1.scrollh {
	top: calc(100% - 180px);
	height: 180px;
}
#arena:not(.chess).oblongcard #handcards1.scrollh {
	top: calc(100% - 200px);
	height: 200px;
}
#arena:not(.chess) #handcards1.scrollh > div {
	height: 120px;
	top: 60px;
}
#arena:not(.chess).oblongcard #handcards1.scrollh > div {
	top: 62px;
}
#arena:not(.chess):not(.single-handcard) #handcards1 {
	width: calc(100% - 240px);
	left: calc(150% / 47 - 300% / 94 + 625% / 47 - 105px + 120px);
}
#arena:not(.single-handcard):not(.chess) > #me,
#arena:not(.single-handcard):not(.chess) > #mebg,
#arena:not(.single-handcard):not(.chess) > #autonode {
	left: 0 !important;
	bottom: 38px !important;
	width: calc(9700% / 94) !important;
}
#arena:not(.single-handcard):not(.chess) > #mebg {
	visibility: hidden;
}
#arena:not(.single-handcard):not(.chess) > #me #handcards1 {
	left: 120px !important;
	width: calc(100% - 120px) !important;
}
#arena:not(.single-handcard):not(.chess) > #me #handcards1 > .handcards {
	left: 0 !important;
}
#autonode {
	display: table !important;
}
@media screen and (max-width: 1105px) {
	#arena[data-number="8"]:not(.single-handcard):not(.chess) > #me,
	#arena[data-number="8"]:not(.single-handcard):not(.chess) > #mebg,
	#arena[data-number="8"]:not(.single-handcard):not(.chess) > #autonode {
		left: calc(-300% / 94 + 625% / 47 - 105px) !important;
		width: calc(9700% / 94 + 300% / 94 - 625% / 47 + 105px) !important;
	}
}
#handcards2 {
	display: none;
}
.dialog {
	height: calc(100% - 370px);
	bottom: 170px;
}
#arena.choose-character > .dialog .placeholder + .placeholder {
	display: none;
}
#arena.choose-character > .dialog .placeholder {
	margin-bottom: 4px;
	height: 0px;
}
#arena.choose-character > .dialog.noupdate .placeholder {
	margin-bottom: 0;
	height: 0;
}
#arena.choose-character > .dialog {
	height: calc(100% - 280px);
	bottom: 80px;
}
#arena.choose-character > .dialog.scroll3 {
	height: calc(100% - 240px);
}
#arena.phone.choose-character > .dialog {
	bottom: 93px;
}
#arena.ipad.choose-character > .dialog {
	bottom: 96px;
}
#arena.choose-to-move > #control,
#arena.discard-player-card > #control,
#arena.gain-player-card > #control,
#arena.choose-player-card > #control,
#arena.choose-character > #control {
	bottom: 30px;
	transition: all 0s;
}
#arena.phone.choose-to-move > #control,
#arena.phone.discard-player-card > #control,
#arena.phone.gain-player-card > #control,
#arena.phone.choose-player-card > #control,
#arena.phone.choose-character > #control {
	bottom: 43px;
}
#arena.ipad.choose-to-move > #control,
#arena.ipad.discard-player-card > #control,
#arena.ipad.gain-player-card > #control,
#arena.ipad.choose-player-card > #control,
#arena.ipad.choose-character > #control {
	bottom: 45px;
}
.dialog.fullheight {
	height: calc(100% - 123px) !important;
	top: 40px !important;
}

#me > .fakeme.avatar {
	width: 120px;
	height: 100%;
	border-radius: 0px;
	top: 0;
	left: 0;
	background-size: cover;
	clip-path: polygon(-10px 0, 130px 0, 130px 130px, -10px 130px);
	-webkit-clip-path: polygon(-10px 0, 130px 0, 130px 130px, -10px 130px);
}
#window[data-radius_size="increase"] #me > .fakeme.avatar,
#window[data-radius_size="reduce"] #me > .fakeme.avatar {
	border-radius: 0px;
}
#arena > .player[data-position="0"]:not(.minskin) {
	top: calc(100% - 258px);
}
#arena[data-player_height="default"] > .player[data-position="0"]:not(.minskin) {
	top: calc(100% - 238px);
}
#arena[data-player_height="short"] > .player[data-position="0"]:not(.minskin) {
	top: calc(100% - 218px);
}

#arena > .player:not(.minskin) > .marks {
	left: -15px;
}
#arena > .player:not(.minskin) > .judges {
	right: -27px;
}

#arena.lslim_player .player .equips {
	left: 5px;
}

#arena > .player:not(.minskin) > .name.name2 {
	left: auto !important;
	right: 13px;
}

#arena > .player:not(.minskin) {
	width: 120px !important;
	height: 220px !important;
}
#arena > .player:not(.minskin) > .damage.dieidentity {
	font-size: 60px;
	transform: scale(1);
}
#arena[data-player_height="default"] > .player:not(.minskin) {
	height: 200px !important;
}
#arena[data-player_height="short"] > .player:not(.minskin) {
	height: 180px !important;
}
#arena > .player:not(.minskin) > .equips {
	transform: scale(0.8);
	transform-origin: bottom left;
}
#arena > .player:not(.minskin) > .avatar,
#arena > .player:not(.minskin) > .avatar2 {
	width: calc(100% - 14px) !important;
	height: calc(100% - 14px) !important;
	background-position: 50% !important;
}
#arena.uslim_player > .player:not(.minskin) > .avatar,
#arena.uslim_player > .player:not(.minskin) > .avatar2 {
	width: calc(100% - 6px) !important;
	height: calc(100% - 6px) !important;
	background-position: 50% !important;
}
#arena.lslim_player > .player:not(.minskin) > .avatar,
#arena.lslim_player > .player:not(.minskin) > .avatar2 {
	width: calc(100% - 10px) !important;
	height: calc(100% - 10px) !important;
	background-position: 50% !important;
}
#arena > .player.fullskin2:not(.minskin) > .avatar,
#arena > .player.fullskin2:not(.minskin) > .avatar2 {
	height: 50% !important;
	background-position: 0 0 !important;
	border-radius: 8px !important;
}
#arena.uslim_player > .player.fullskin2:not(.minskin) > .avatar,
#arena.uslim_player > .player.fullskin2:not(.minskin) > .avatar2 {
	height: calc(50% + 4px) !important;
}
#arena.lslim_player > .player.fullskin2:not(.minskin) > .avatar,
#arena.lslim_player > .player.fullskin2:not(.minskin) > .avatar2 {
	height: calc(50% + 2px) !important;
}
#window[data-radius_size="reduce"] #arena > .player.fullskin2:not(.minskin) > .avatar,
#window[data-radius_size="reduce"] #arena > .player.fullskin2:not(.minskin) > .avatar2 {
	border-radius: 4px !important;
}
#window[data-radius_size="off"] #arena > .player.fullskin2:not(.minskin) > .avatar,
#window[data-radius_size="off"] #arena > .player.fullskin2:not(.minskin) > .avatar2 {
	border-radius: 0px !important;
}
#window[data-radius_size="increase"] #arena > .player.fullskin2:not(.minskin) > .avatar,
#window[data-radius_size="increase"] #arena > .player.fullskin2:not(.minskin) > .avatar2 {
	border-radius: 16px !important;
}
#arena > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#arena > .player.fullskin2:not(.minskin).unseen2[data-position="0"] > .avatar {
	border-radius: 8px 8px 0 0 !important;
	height: calc(50% + 14px) !important;
	clip-path: polygon(-10px -10px, 116px -10px, 116px 92px, 106px 92px, 0px 114px, -10px 114px);
	-webkit-clip-path: polygon(-10px -10px, 116px -10px, 116px 92px, 106px 92px, 0px 114px, -10px 114px);
}
#arena[data-player_height="default"] > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#arena[data-player_height="default"] > .player.fullskin2:not(.minskin).unseen2[data-position="0"] > .avatar {
	clip-path: polygon(-10px -10px, 116px -10px, 116px 82px, 106px 82px, 0px 104px, -10px 104px);
	-webkit-clip-path: polygon(-10px -10px, 116px -10px, 116px 82px, 106px 82px, 0px 104px, -10px 104px);
}
#arena[data-player_height="short"] > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#arena[data-player_height="short"] > .player.fullskin2:not(.minskin).unseen2[data-position="0"] > .avatar {
	clip-path: polygon(-10px -10px, 116px -10px, 116px 72px, 106px 72px, 0px 94px, -10px 94px);
	-webkit-clip-path: polygon(-10px -10px, 116px -10px, 116px 72px, 106px 72px, 0px 94px, -10px 94px);
}
#arena.uslim_player > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#arena.uslim_player > .player.fullskin2:not(.minskin).unseen2[data-position="0"] > .avatar {
	height: calc(50% + 18px) !important;
	clip-path: polygon(-10px -10px, 124px -10px, 124px 96px, 114px 96px, 0px 118px, -10px 118px);
	-webkit-clip-path: polygon(-10px -10px, 124px -10px, 124px 96px, 114px 96px, 0px 118px, -10px 118px);
}
#arena.uslim_player[data-player_height="default"] > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#arena.uslim_player[data-player_height="default"]
	> .player.fullskin2:not(.minskin).unseen2[data-position="0"]
	> .avatar {
	clip-path: polygon(-10px -10px, 124px -10px, 124px 86px, 114px 86px, 0px 108px, -10px 108px);
	-webkit-clip-path: polygon(-10px -10px, 124px -10px, 124px 86px, 114px 86px, 0px 108px, -10px 108px);
}
#arena.uslim_player[data-player_height="short"] > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#arena.uslim_player[data-player_height="short"]
	> .player.fullskin2:not(.minskin).unseen2[data-position="0"]
	> .avatar {
	clip-path: polygon(-10px -10px, 124px -10px, 124px 76px, 114px 76px, 0px 98px, -10px 98px);
	-webkit-clip-path: polygon(-10px -10px, 124px -10px, 124px 76px, 114px 76px, 0px 98px, -10px 98px);
}
#arena.lslim_player > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#arena.lslim_player > .player.fullskin2:not(.minskin).unseen2[data-position="0"] > .avatar {
	height: calc(50% + 16px) !important;
	clip-path: polygon(-10px -10px, 120px -10px, 120px 94px, 110px 94px, 0px 116px, -10px 116px);
	-webkit-clip-path: polygon(-10px -10px, 120px -10px, 120px 94px, 110px 94px, 0px 116px, -10px 116px);
}
#arena.lslim_player[data-player_height="default"] > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#arena.lslim_player[data-player_height="default"]
	> .player.fullskin2:not(.minskin).unseen2[data-position="0"]
	> .avatar {
	clip-path: polygon(-10px -10px, 120px -10px, 120px 84px, 110px 84px, 0px 106px, -10px 106px);
	-webkit-clip-path: polygon(-10px -10px, 120px -10px, 120px 84px, 110px 84px, 0px 106px, -10px 106px);
}
#arena.lslim_player[data-player_height="short"] > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#arena.lslim_player[data-player_height="short"]
	> .player.fullskin2:not(.minskin).unseen2[data-position="0"]
	> .avatar {
	clip-path: polygon(-10px -10px, 120px -10px, 120px 74px, 110px 74px, 0px 96px, -10px 96px);
	-webkit-clip-path: polygon(-10px -10px, 120px -10px, 120px 74px, 110px 74px, 0px 96px, -10px 96px);
}
#window[data-radius_size="reduce"] #arena > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#window[data-radius_size="reduce"]
	#arena
	> .player.fullskin2:not(.minskin).unseen2[data-position="0"]
	> .avatar {
	border-radius: 4px 4px 0 0 !important;
}
#window[data-radius_size="off"] #arena > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#window[data-radius_size="off"]
	#arena
	> .player.fullskin2:not(.minskin).unseen2[data-position="0"]
	> .avatar {
	border-radius: 0 0 0 0 !important;
}
#window[data-radius_size="increase"] #arena > .player.fullskin2:not(.minskin):not(.unseen2) > .avatar,
#window[data-radius_size="increase"]
	#arena
	> .player.fullskin2:not(.minskin).unseen2[data-position="0"]
	> .avatar {
	border-radius: 16px 16px 0 0 !important;
}
#arena > .player.fullskin2:not(.minskin):not(.unseen) > .avatar2,
#arena > .player.fullskin2:not(.minskin).unseen[data-position="0"] > .avatar2 {
	border-radius: 0 0 8px 8px !important;
	top: calc(50% - 21px) !important;
	height: calc(50% + 14px) !important;
	background-position: 0 10px !important;
	clip-path: polygon(-10px 32px, 0 32px, 106px 10px, 116px 10px, 116px 134px, -10px 134px);
	-webkit-clip-path: polygon(-10px 32px, 0 32px, 106px 10px, 116px 10px, 116px 134px, -10px 134px);
}
#arena.uslim_player > .player.fullskin2:not(.minskin):not(.unseen) > .avatar2,
#arena.uslim_player > .player.fullskin2:not(.minskin).unseen[data-position="0"] > .avatar2 {
	top: calc(50% - 21px) !important;
	height: calc(50% + 18px) !important;
	clip-path: polygon(-10px 32px, 0 32px, 114px 10px, 124px 10px, 124px 138px, -10px 138px);
	-webkit-clip-path: polygon(-10px 32px, 0 32px, 114px 10px, 124px 10px, 124px 138px, -10px 138px);
}
#arena.lslim_player > .player.fullskin2:not(.minskin):not(.unseen) > .avatar2,
#arena.lslim_player > .player.fullskin2:not(.minskin).unseen[data-position="0"] > .avatar2 {
	top: calc(50% - 21px) !important;
	height: calc(50% + 16px) !important;
	clip-path: polygon(-10px 32px, 0 32px, 110px 10px, 120px 10px, 120px 136px, -10px 136px);
	-webkit-clip-path: polygon(-10px 32px, 0 32px, 110px 10px, 120px 10px, 120px 136px, -10px 136px);
}
#window[data-radius_size="reduce"] #arena > .player.fullskin2:not(.minskin):not(.unseen) > .avatar2,
#window[data-radius_size="reduce"]
	#arena
	> .player.fullskin2:not(.minskin).unseen[data-position="0"]
	> .avatar2 {
	border-radius: 0 0 4px 4px !important;
}
#window[data-radius_size="off"] #arena > .player.fullskin2:not(.minskin):not(.unseen) > .avatar2,
#window[data-radius_size="off"]
	#arena
	> .player.fullskin2:not(.minskin).unseen[data-position="0"]
	> .avatar2 {
	border-radius: 0 0 0 0 !important;
}
#window[data-radius_size="increase"] #arena > .player.fullskin2:not(.minskin):not(.unseen) > .avatar2,
#window[data-radius_size="increase"]
	#arena
	> .player.fullskin2:not(.minskin).unseen[data-position="0"]
	> .avatar2 {
	border-radius: 0 0 16px 16px !important;
}
#arena > .player.fullskin2:not(.minskin) > .avatar2 {
	top: calc(50% - 7px) !important;
}
#arena > .player:not(.minskin) > .identity {
	left: 102px;
}
#arena > .player:not(.minskin) > .hp:not(.actcount) {
	left: 93px;
}
/*#arena>.player:not(.minskin)>.hp:not(.actcount).text{
    left: 89px;
}*/
#arena > .player.fullskin2 .avatar2 {
	z-index: 2;
}
#arena > .player.unseen:not(.unseen2) .count {
	text-align: left;
	border-radius: 3px 0 0 3px;
}
#arena > .player.unseen2 .count {
	border-radius: 3px;
	text-align: center;
}

#arena .timerbar > div {
	top: 205px;
	width: 96px;
	left: 12px;
}
#arena[data-player_height="default"] .timerbar > div {
	top: 185px;
}
#arena[data-player_height="short"] .timerbar > div {
	top: 165px;
}
#arena .player[data-position="0"] > .damage.dieidentity {
	opacity: 1 !important;
}

/*--------位置(8人)------*/
#arena[data-number="8"] > .player[data-position="1"] {
	top: calc(30% - 128px);
	left: calc(-300% / 94 + 4375% / 47 - 735px + 720px);
}
#arena[data-number="8"] > .player[data-position="2"] {
	top: calc(8% - 34px);
	left: calc(-300% / 94 + 3750% / 47 - 630px + 600px);
}
#arena[data-number="8"] > .player[data-position="3"] {
	top: 0;
	left: calc(-300% / 94 + 3125% / 47 - 525px + 480px);
}
#arena[data-number="8"] > .player[data-position="4"] {
	top: 0;
	left: calc(-300% / 94 + 2500% / 47 - 420px + 360px);
}
#arena[data-number="8"] > .player[data-position="5"] {
	top: 0;
	left: calc(-300% / 94 + 1875% / 47 - 315px + 240px);
}
#arena[data-number="8"] > .player[data-position="6"] {
	top: calc(8% - 34px);
	left: calc(-300% / 94 + 1250% / 47 - 210px + 120px);
}
#arena[data-number="8"] > .player[data-position="7"] {
	top: calc(30% - 128px);
	left: calc(-300% / 94 + 625% / 47 - 105px);
}
#arena[data-number="8"] > .player[data-position="0"] {
	left: calc(-300% / 94 + 625% / 47 - 105px);
}
#arena > .player[data-position="0"] {
	left: 0;
}
@media screen and (min-width: 1105px) {
	#arena[data-number="8"] > .player[data-position="1"] {
		left: calc(100% - 120px);
	}
	#arena[data-number="8"] > .player[data-position="2"] {
		left: calc(500% / 6 - 100px);
	}
	#arena[data-number="8"] > .player[data-position="3"] {
		left: calc(400% / 6 - 80px);
	}
	#arena[data-number="8"] > .player[data-position="4"] {
		left: calc(300% / 6 - 60px);
	}
	#arena[data-number="8"] > .player[data-position="5"] {
		left: calc(200% / 6 - 40px);
	}
	#arena[data-number="8"] > .player[data-position="6"] {
		left: calc(100% / 6 - 20px);
	}
	#arena[data-number="8"] > .player[data-position="7"] {
		left: 0;
	}
	#arena[data-number="8"] > .player[data-position="0"] {
		left: 0;
	}
}
/*--------位置(7人)------*/
#arena[data-number="7"] > .player[data-position="1"] {
	top: calc(30% - 128px);
	left: calc(100% - 120px);
}
#arena[data-number="7"] > .player[data-position="2"] {
	top: calc(8% - 34px);
	left: calc(80% - 96px);
}
#arena[data-number="7"] > .player[data-position="3"] {
	top: 0;
	left: calc(60% - 72px);
}
#arena[data-number="7"] > .player[data-position="4"] {
	top: 0;
	left: calc(40% - 48px);
}
#arena[data-number="7"] > .player[data-position="5"] {
	top: calc(8% - 34px);
	left: calc(20% - 24px);
}
#arena[data-number="7"] > .player[data-position="6"] {
	top: calc(30% - 128px);
	left: 0;
}
/*--------位置(6人)------*/
#arena[data-number="6"] > .player[data-position="1"] {
	top: calc(30% - 128px);
	left: calc(100% - 120px);
}
#arena[data-number="6"] > .player[data-position="2"] {
	top: 0px;
	left: calc(75% - 90px);
}
#arena[data-number="6"] > .player[data-position="3"] {
	top: 0;
	left: calc(50% - 60px);
}
#arena[data-number="6"] > .player[data-position="4"] {
	top: 0px;
	left: calc(25% - 30px);
}
#arena[data-number="6"] > .player[data-position="5"] {
	top: calc(30% - 128px);
	left: 0;
}
/*--------位置(5人)------*/
#arena[data-number="5"] > .player[data-position="1"] {
	top: calc(30% - 128px);
	left: calc(100% - 120px);
}
#arena[data-number="5"] > .player[data-position="2"] {
	top: 0;
	left: calc(200% / 3 - 80px);
}
#arena[data-number="5"] > .player[data-position="3"] {
	top: 0;
	left: calc(100% / 3 - 40px);
}
#arena[data-number="5"] > .player[data-position="4"] {
	top: calc(30% - 128px);
	left: 0;
}
/*--------位置(4人)------*/
#arena[data-number="4"] > .player[data-position="1"] {
	top: calc(30% - 128px);
	left: calc(100% - 120px);
}
#arena[data-number="4"] > .player[data-position="2"] {
	top: 0;
	left: calc(50% - 60px);
}
#arena[data-number="4"] > .player[data-position="3"] {
	top: calc(30% - 128px);
	left: 0;
}
/*--------位置(3人)------*/
#arena[data-number="3"] > .player[data-position="1"] {
	top: calc(60% / 3 - 88px);
	left: calc(75% + 80px);
}
#arena[data-number="3"] > .player[data-position="2"] {
	top: calc(60% / 3 - 88px);
	left: calc(25% - 200px);
}
/*--------位置(2人)------*/
#arena[data-number="2"] > .player[data-position="1"] {
	top: 0;
	left: calc(50% - 60px);
}
