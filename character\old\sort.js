const characterSort = {
	old_standard: ["ol_yuanshu"],
	old_shenhua: ["old_caocao", "yuji", "zhang<PERSON><PERSON>", "old_zhugezhan", "old_guanqiujian", "x<PERSON><PERSON><PERSON>", "weiyan", "old_xia<PERSON><PERSON><PERSON>", "pangde", "xuhuang", "huangzhong", "new_caoren", "old_chendao"],
	old_refresh: ["old_zhang<PERSON>i", "old_huatuo", "old_zhaoyun", "ol_huaxiong", "old_guanyu"],
	old_yijiang1: ["masu", "xushu", "xin_yujin", "old_xusheng", "old_lingtong", "fazheng", "old_gaoshun", "re_yujin"],
	old_yijiang2: ["old_zhonghui", "madai", "old_handang", "old_liubiao", "oldre_liubiao", "old_guanzhang", "old_wangyi"],
	old_yijiang3: ["liru", "old_zhuran", "old_fuhuanghou", "old_caochong"],
	old_yijiang4: ["old_caozhen", "old_chenqun", "old_zhuhuan", "old_caorui", "old_wuyi"],
	old_yijiang5: ["old_caoxiu", "old_zhuzhi"],
	old_yijiang67: ["old_huanghao", "old_liyan"],
	old_sp: ["old_shixie", "panfeng", "old_wanglang", "old_maliang", "old_zhangxingcai", "old_wangyun", "old_dingfeng", "old_guanyinping"],
	old_yingbian: ["junk_simayi", "old_yangyan", "old_yangzhi"],
	old_mobile: ["old_caochun"],
};

const characterSortTranslate = {
	old_standard: "标准包",
	old_shenhua: "神话再临",
	old_refresh: "界限突破",
	old_yijiang1: "一将成名2011",
	old_yijiang2: "一将成名2012",
	old_yijiang3: "一将成名2013",
	old_yijiang4: "一将成名2014",
	old_yijiang5: "一将成名2015",
	old_yijiang67: "原创设计",
	old_sp: "SP",
	old_yingbian: "文德武备",
	old_mobile: "移动版",
};

export { characterSort, characterSortTranslate };
