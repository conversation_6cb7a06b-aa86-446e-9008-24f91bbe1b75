import { lib, game, ui, get, ai, _status } from "../../noname.js";

const cards = {
	cheliji_sichengliangyu: {
		fullskin: true,
		vanish: true,
		derivation: "cheliji",
		destroy: "chexuan",
		type: "equip",
		subtype: "equip5",
		cardcolor: "heart",
		skills: ["cheliji_sichengliangyu"],
	},
	cheliji_tiejixuanyu: {
		fullskin: true,
		vanish: true,
		derivation: "cheliji",
		destroy: "chexuan",
		type: "equip",
		subtype: "equip5",
		cardcolor: "club",
		skills: ["cheliji_tiejixuanyu"],
	},
	cheliji_feilunzhanyu: {
		fullskin: true,
		vanish: true,
		derivation: "cheliji",
		destroy: "chexuan",
		type: "equip",
		subtype: "equip5",
		cardcolor: "spade",
		skills: ["cheliji_feilunzhanyu"],
	},
};

export default cards;
