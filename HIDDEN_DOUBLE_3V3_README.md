# 暗身份双将3V3游戏模式

## 📋 模式概述

暗身份双将3V3是一个全新的无名杀游戏模式，结合了暗身份机制和双将系统，为6名玩家提供了独特的推理和策略体验。

## 🎮 游戏设置

### 基本配置
- **玩家数量**: 6人
- **身份分配**: 1个主公、2个忠臣、1内奸、2反贼
- **双将模式**: 强制开启
- **武将包限制**: 2008版标准包 + 神话再临(风火林山)包

### 身份机制
- **暗身份**: 游戏开始时所有身份都是隐藏的
- **自知身份**: 每个玩家只知道自己的身份
- **身份显示**: 其他玩家的身份显示为"？"（完全未知状态）
- **无猜测功能**: 移除了身份猜测和自动标记功能

## ⚔️ 双将系统

### 武将选择
- 每人从6张随机武将中选择2个
- 武将来源限定为以下包：
  - **2008版标准包**: 22个武将（曹操、刘备、孙权等经典武将，去除卧龙诸葛亮、黄盖、华佗）
  - **神话再临-风**: 7个武将（张角、周泰、曹仁等，去除于吉）
  - **神话再临-火**: 8个武将（典韦、荀彧、庞统等）
  - **神话再临-林**: 8个武将（曹丕、徐晃、孟获等）
  - **神话再临-山**: 8个武将（邓艾、张郃、刘禅等）
  - **额外武将**: 2个武将（SP袁术、杨修）
- 总计55个武将，确保武将分配的公平性和平衡性

### 血量计算
- **计算方式**: 两单将血量相加减3
- **示例**: 
  - 吕布(4血) + 郭嘉(3血) = 4血
  - 吕布(4血) + 曹操(4血) = 5血

## 🔍 身份查看机制

### 查看条件
- 当任意角色将另一名角色的血量变为1时
- 自动触发身份查看机制
- 适用于攻击和治疗等所有血量变化

### 身份揭示
- 身份一旦被查看就会在日志和身份框中显示
- 被揭示的身份对所有玩家公开可见
- 已揭示的身份不会重复触发查看机制
- 游戏结束时所有身份都会被显示

## 🏆 势力与胜负

### 势力划分
- **忠臣势力**: 主公 + 忠臣 (3人)
- **反贼势力**: 内奸 + 反贼 (3人)

### 胜利条件
- **主公死亡**: 反贼势力(内奸+反贼)获胜
- **内奸死亡**: 忠臣势力(主公+忠臣)获胜

### 特殊规则
- 游戏在主公或内奸死亡时立即结束
- 不需要消灭所有敌对角色
- 其他角色（忠臣、反贼）死亡时游戏继续
- 游戏结束时所有身份会被公开显示

## 🤖 AI优化

### 智能行为
- AI会根据已知身份信息调整策略
- 对未知身份的角色保持相对中性的态度
- 同势力角色之间会相互配合

### 态度计算
- **身份已知**: 使用完整的态度值
- **身份未知**: 大幅降低态度值，保持谨慎

## 🎯 游戏特色

### 核心特点
1. **推理乐趣**: 通过观察行为和身份揭示进行推理
2. **策略深度**: 双将组合提供丰富的策略选择
3. **平衡对抗**: 3V3的势力划分确保游戏平衡
4. **渐进揭示**: 身份信息逐步公开，增加游戏张力

### 技术实现
- 完整的身份隐藏机制
- 智能的身份查看系统
- 优化的AI行为逻辑
- 平衡的武将选择机制

## 🚀 如何开始

1. 启动无名杀游戏
2. 选择"身份模式"
3. 在游戏模式中选择"暗身份双将3V3"
4. 等待6名玩家加入
5. 开始游戏，享受全新体验！

## 📝 更新日志

### v1.0 (当前版本)
- ✅ 实现基本的暗身份双将3V3模式
- ✅ 优化身份显示逻辑
- ✅ 修正6人游戏身份分配
- ✅ 改进身份查看机制
- ✅ 移除自动身份判断和猜测功能
- ✅ 完善身份揭示的日志显示
- ✅ 精确限制武将包为2008版标准包+神话再临风火林山
- ✅ 调整武将池：移除卧龙诸葛亮、黄盖、于吉、华佗，添加SP袁术、杨修
- ✅ 优化AI行为逻辑
- ✅ 完善游戏结束判断逻辑
- ✅ 添加完整的帮助文档

## 🔧 技术细节

### 文件修改
- `mode/identity.js`: 主要游戏逻辑
- `noname/library/index.js`: 配置选项
- `game/config.js`: 身份配置

### 关键函数
- `chooseCharacterHiddenDouble3v3()`: 双将选择
- `hidden_double_3v3_identity_check`: 身份查看技能
- `realAttitude()`: AI态度计算
- `checkResult()`: 胜负判定

---

**制作**: 基于无名杀框架开发  
**版本**: 1.0  
**更新时间**: 2025-06-14
