// 暗身份双将3V3模式武将池最终验证
console.log("=== 暗身份双将3V3模式武将池最终验证 ===");

// 最终的武将列表（与游戏代码完全一致）
const finalCharacterList = {
    // 2008版标准包武将（去除卧龙诸葛亮、黄盖、华佗）
    standard2008: ["caocao", "simayi", "xiahoudun", "zhangliao", "xuzhu", "guojia", "zhenji", "liubei", "guanyu", "zhangfei", "zhaoyun", "machao", "huangyueying", "sunquan", "ganning", "lvmeng", "zhouyu", "daqiao", "luxun", "sunshangxiang", "lvbu", "diaochan"],
    
    // 神话再临-风包武将（去除于吉）
    shenhuaFeng: ["sp_zhangjiao", "old_zhoutai", "old_caoren", "re_x<PERSON><PERSON><PERSON>", "xia<PERSON><PERSON><PERSON>", "re_huangzhong", "re_weiyan"],
    
    // 神话再临-火包武将
    shenhuaHuo: ["dianwei", "xunyu", "pangtong", "sp_zhugeliang", "taishici", "yanwen", "re_yuanshao", "re_pangde"],
    
    // 神话再临-林包武将
    shenhuaLin: ["caopi", "re_xuhuang", "menghuo", "zhurong", "re_lusu", "sunjian", "dongzhuo", "jiaxu"],
    
    // 神话再临-山包武将
    shenhuaShan: ["dengai", "zhanghe", "liushan", "jiangwei", "zhangzhang", "sunce", "caiwenji", "zuoci"],
    
    // 额外添加的武将（SP袁术、杨修）
    extraCharacters: ["re_yuanshu", "yangxiu"]
};

// 合并所有武将
const allCharacters = [].concat(
    finalCharacterList.standard2008,
    finalCharacterList.shenhuaFeng,
    finalCharacterList.shenhuaHuo,
    finalCharacterList.shenhuaLin,
    finalCharacterList.shenhuaShan,
    finalCharacterList.extraCharacters
);

// 武将中文名称映射
const characterNames = {
    // 魏国
    caocao: "曹操", simayi: "司马懿", xiahoudun: "夏侯惇", zhangliao: "张辽", xuzhu: "许褚",
    guojia: "郭嘉", zhenji: "甄姬", dianwei: "典韦", xunyu: "荀彧", old_caoren: "曹仁",
    re_xiahouyuan: "夏侯渊", caopi: "曹丕", re_xuhuang: "徐晃", dengai: "邓艾", zhanghe: "张郃",
    
    // 蜀国
    liubei: "刘备", guanyu: "关羽", zhangfei: "张飞", zhaoyun: "赵云", machao: "马超",
    huangyueying: "黄月英", pangtong: "庞统", sp_zhugeliang: "火诸葛亮", re_huangzhong: "黄忠",
    re_weiyan: "魏延", liushan: "刘禅", jiangwei: "姜维",
    
    // 吴国
    sunquan: "孙权", ganning: "甘宁", lvmeng: "吕蒙", zhouyu: "周瑜", daqiao: "大乔",
    luxun: "陆逊", sunshangxiang: "孙尚香", old_zhoutai: "周泰", xiaoqiao: "小乔",
    taishici: "太史慈", re_lusu: "鲁肃", sunjian: "孙坚", zhangzhang: "张昭张纮", sunce: "孙策",
    
    // 群雄
    lvbu: "吕布", diaochan: "貂蝉", sp_zhangjiao: "张角", yanwen: "颜良文丑",
    re_yuanshao: "袁绍", re_pangde: "庞德", menghuo: "孟获", zhurong: "祝融",
    dongzhuo: "董卓", jiaxu: "贾诩", caiwenji: "蔡文姬", zuoci: "左慈",
    re_yuanshu: "SP袁术", yangxiu: "杨修"
};

console.log("\n=== 武将包统计 ===");
console.log(`2008版标准包: ${finalCharacterList.standard2008.length}个武将`);
console.log(`神话再临-风: ${finalCharacterList.shenhuaFeng.length}个武将`);
console.log(`神话再临-火: ${finalCharacterList.shenhuaHuo.length}个武将`);
console.log(`神话再临-林: ${finalCharacterList.shenhuaLin.length}个武将`);
console.log(`神话再临-山: ${finalCharacterList.shenhuaShan.length}个武将`);
console.log(`额外武将: ${finalCharacterList.extraCharacters.length}个武将`);
console.log(`总计: ${allCharacters.length}个武将`);

// 验证移除的武将确实不在列表中
console.log("\n=== 移除武将验证 ===");
const removedCharacters = ["zhugeliang", "huanggai", "re_yuji", "huatuo"];
const removedNames = ["卧龙诸葛亮", "黄盖", "于吉", "华佗"];

removedCharacters.forEach((char, index) => {
    const isRemoved = !allCharacters.includes(char);
    const status = isRemoved ? '✅' : '❌';
    console.log(`${status} ${removedNames[index]} (${char}): ${isRemoved ? '已移除' : '仍存在'}`);
});

// 验证添加的武将确实在列表中
console.log("\n=== 添加武将验证 ===");
const addedCharacters = ["re_yuanshu", "yangxiu"];
const addedNames = ["SP袁术", "杨修"];

addedCharacters.forEach((char, index) => {
    const isAdded = allCharacters.includes(char);
    const status = isAdded ? '✅' : '❌';
    console.log(`${status} ${addedNames[index]} (${char}): ${isAdded ? '已添加' : '未找到'}`);
});

// 验证游戏可行性
console.log("\n=== 游戏可行性验证 ===");
const requiredCharacters = 6 * 6; // 6人每人6张武将
console.log(`需要武将数量: ${requiredCharacters}个`);
console.log(`可用武将数量: ${allCharacters.length}个`);
console.log(`剩余武将数量: ${allCharacters.length - requiredCharacters}个`);
console.log(`是否足够: ${allCharacters.length >= requiredCharacters ? '✅ 是' : '❌ 否'}`);

// 按势力分类统计
console.log("\n=== 势力分布统计 ===");
const factionCount = { wei: 0, shu: 0, wu: 0, qun: 0 };

// 简化的势力判断（基于武将名称）
const weiCharacters = ["caocao", "simayi", "xiahoudun", "zhangliao", "xuzhu", "guojia", "zhenji", "dianwei", "xunyu", "old_caoren", "re_xiahouyuan", "caopi", "re_xuhuang", "dengai", "zhanghe"];
const shuCharacters = ["liubei", "guanyu", "zhangfei", "zhaoyun", "machao", "huangyueying", "pangtong", "sp_zhugeliang", "re_huangzhong", "re_weiyan", "liushan", "jiangwei"];
const wuCharacters = ["sunquan", "ganning", "lvmeng", "zhouyu", "daqiao", "luxun", "sunshangxiang", "old_zhoutai", "xiaoqiao", "taishici", "re_lusu", "sunjian", "zhangzhang", "sunce"];

allCharacters.forEach(char => {
    if (weiCharacters.includes(char)) factionCount.wei++;
    else if (shuCharacters.includes(char)) factionCount.shu++;
    else if (wuCharacters.includes(char)) factionCount.wu++;
    else factionCount.qun++;
});

console.log(`魏国: ${factionCount.wei}个武将`);
console.log(`蜀国: ${factionCount.shu}个武将`);
console.log(`吴国: ${factionCount.wu}个武将`);
console.log(`群雄: ${factionCount.qun}个武将`);

// 显示完整武将列表
console.log("\n=== 完整武将列表 ===");
console.log("2008版标准包 (22个):");
finalCharacterList.standard2008.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n神话再临-风 (7个):");
finalCharacterList.shenhuaFeng.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n神话再临-火 (8个):");
finalCharacterList.shenhuaHuo.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n神话再临-林 (8个):");
finalCharacterList.shenhuaLin.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n神话再临-山 (8个):");
finalCharacterList.shenhuaShan.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n额外武将 (2个):");
finalCharacterList.extraCharacters.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

// 最终验证结果
console.log("\n=== 最终验证结果 ===");
const finalChecks = [
    { name: '移除卧龙诸葛亮', pass: !allCharacters.includes('zhugeliang') },
    { name: '移除黄盖', pass: !allCharacters.includes('huanggai') },
    { name: '移除于吉', pass: !allCharacters.includes('re_yuji') },
    { name: '移除华佗', pass: !allCharacters.includes('huatuo') },
    { name: '添加SP袁术', pass: allCharacters.includes('re_yuanshu') },
    { name: '添加杨修', pass: allCharacters.includes('yangxiu') },
    { name: '武将数量足够', pass: allCharacters.length >= 36 },
    { name: '总数正确', pass: allCharacters.length === 55 },
    { name: '无重复武将', pass: allCharacters.length === [...new Set(allCharacters)].length }
];

let passedChecks = 0;
finalChecks.forEach(check => {
    const status = check.pass ? '✅' : '❌';
    console.log(`${status} ${check.name}`);
    if (check.pass) passedChecks++;
});

console.log(`\n通过检查: ${passedChecks}/${finalChecks.length}`);
if (passedChecks === finalChecks.length) {
    console.log('🎉 所有验证通过！武将池调整完成！');
    console.log('✨ 游戏代码中的武将列表已正确更新');
} else {
    console.log('⚠️ 部分验证失败，需要检查配置。');
}

console.log('\n=== 验证完成 ===');
