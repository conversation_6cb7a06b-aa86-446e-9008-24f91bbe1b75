#arena {
	height: calc(97% + 30px);
	top: 3%;
}
#arena.mobile > #control {
	top: calc(100% - 205px);
}
#arena.chess.mobile > #control {
	top: calc(100% - 175px);
}
#control > div,
#system > div > div {
	height: 40px;
	font-family: "xinwei";
	font-size: 30px;
	line-height: 34px;
}
#historybar {
	top: 14px;
	height: calc(100% - 150px);
}
#window.oblongcard #historybar {
	height: calc(100% - 170px);
}
#time {
	visibility: hidden;
}
#system {
	z-index: 31 !important;
}
#window > .dialog.popped {
	z-index: 21 !important;
}
#system > div {
	position: absolute;
	height: 60px;
}
#system > div:first-child {
	left: 5px;
}
#system > div:last-child {
	right: 5px;
}
#system > div > div {
	line-height: 40px;
}
#system {
	z-index: 5;
}
#system > div {
	transition-duration: 0.3s;
}
#system > div:not(.shown) {
	transform: translateY(-80px);
}
.statusbar #system > div:not(.shown) {
	transform: translateY(-120px);
}
#system > #system1 {
	width: 140px;
}
#system > #system2 {
	width: calc(100% - 140px);
}
#system > div:not(.shown) > div {
	pointer-events: none;
}
#pausebutton,
#autobutton,
#restartbutton {
	display: none !important;
}

/* .menu-container > .menu.main,
.popup-container > .menu {
	zoom: 1.3;
} */
.popup-container > .menu {
	max-height: 307px;
}
.popup-container > .menu.visual.withbar {
	max-height: 360px;
}
.menu-container > .menu.main:not(.center) {
	top: 12px !important;
	left: 10px !important;
}

#system {
	width: calc(100% - 12px) !important;
	left: 0 !important;
}

#arena.phonetop {
	top: 80px !important;
}
.player > .identity {
	transform: scale(1.3);
}
#window > .dialog.popped.hoverdialog {
	transform: scale(1.3);
	transform-origin: left top;
}
.player:not([data-position="0"]).linked .identity,
#arena.chess .player.linked .identity {
	transform: scale(1.3) rotate(90deg);
}

/*br.finish_game{
    display: inline !important;
}*/
div:not(.shown) > div.finish_game {
	opacity: 0 !important;
}
div.finish_game {
	transition: all 0.2s !important;
}

@media screen and (orientation: portrait) {
}
