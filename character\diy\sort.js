const characterSort = {
	diy_yijiang: ["ns_h<PERSON><PERSON><PERSON>", "ns_sun<PERSON><PERSON><PERSON>", "ns_yuan<PERSON>", "ns_ca<PERSON><PERSON>"],
	diy_yijiang2: ["ns_chentai", "ns_h<PERSON><PERSON><PERSON>", "ns_sunyi", "ns_z<PERSON><PERSON>", "ns_yanghu"],
	diy_yijiang3: ["ns_ruanji", "ns_zanghong", "ns_limi", "ns_zhonglimu", "prp_zhugeliang"],
	diy_tieba: ["ns_zuoci", "ns_lvzhi", "ns_wangyun", "ns_nanhua", "ns_nanhua_left", "ns_nanhua_right", "ns_huamulan", "ns_huang<PERSON>", "ns_jinke", "ns_yanliang", "ns_wenchou", "ns_caocao", "ns_caocao<PERSON>", "ns_zhu<PERSON>iang", "ns_wangyu<PERSON>", "ns_yuji", "ns_x<PERSON><PERSON><PERSON><PERSON>", "ns_guanlu", "ns_si<PERSON><PERSON><PERSON>", "ns_sunji<PERSON>", "ns_duang<PERSON>", "ns_z<PERSON><PERSON>", "ns_masu", "ns_zhangxiu", "ns_lvmeng", "ns_shenpei", "ns_yujisp", "ns_yangyi", "ns_liuzhang", "ns_xinnanhua", "ns_luyusheng"],
	diy_fakenews: ["diy_wenyang", "ns_zhangwei", "ns_caimao", "ns_chengpu", "ns_sundeng", "ns_duji", "ns_mengyou"],
	diy_xushi: ["diy_feishi", "diy_hanlong", "diy_liufu", "diy_liuyan", "diy_liuzan", "diy_tianyu", "diy_xizhenxihong", "diy_yangyi", "diy_zaozhirenjun"],
	diy_default: ["diy_luxun", "diy_yuji", "diy_zhouyu", "diy_caiwenji", "diy_lukang", "diy_zhenji", "old_majun"],
	diy_noname: ["noname", "ns_shijian"],
	diy_trashbin: ["junk_guanyu", "junk_zhangrang", "old_bulianshi", "ol_maliang", "junk_liubei", "junk_huangyueying", "junk_lidian", "junk_duanwei", "junk_xuyou", "junk_liuyan", "zhangren"],
};

const characterSortTranslate = {
	diy_tieba: "吧友设计",
	diy_xushi: "玩点论杀·虚实篇",
	diy_default: "常规",
	diy_noname: "无名专属",
	diy_key: "论外",
	diy_yijiang: "设计比赛2020",
	diy_yijiang2: "设计比赛2021",
	diy_yijiang3: "设计比赛2022",
	diy_fakenews: "杀海流言",
	diy_trashbin: "垃圾桶",
};

export { characterSort, characterSortTranslate };
