.toast-container {
    display: block;
    position: absolute;
    top: 15%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
}

.toast {
    display: block;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 165, 0, 0.7);
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    color: white;
    font-weight: bold;
    padding: 10px 20px;
    text-align: center;
    white-space: nowrap;
    width: max-content;
    animation: fadeInOut 1500ms ease-in-out forwards;
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
    }

    20% {
        opacity: 1;
    }

    80% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}