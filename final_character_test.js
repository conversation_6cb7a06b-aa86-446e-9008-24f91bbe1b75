// 暗身份双将3V3模式武将选择最终验证
console.log("=== 暗身份双将3V3模式武将选择最终验证 ===");

// 定义完整的武将列表（与游戏代码中的完全一致）
const characterPacks = {
    standard2008: ["caocao", "simayi", "xiah<PERSON>un", "zhang<PERSON>o", "xuzhu", "guojia", "zhenji", "liubei", "guanyu", "zhangfei", "zhugeliang", "zhaoyun", "machao", "huangyueying", "sunquan", "ganning", "lvmeng", "huanggai", "zhouyu", "daqiao", "luxun", "sunshangxiang", "huatuo", "lvbu", "diaochan"],
    shenhua<PERSON><PERSON>: ["sp_zhangjiao", "re_yuji", "old_zhoutai", "old_caoren", "re_x<PERSON><PERSON><PERSON>", "xia<PERSON><PERSON><PERSON>", "re_huang<PERSON><PERSON>", "re_weiyan"],
    she<PERSON><PERSON><PERSON><PERSON>: ["dianwei", "xunyu", "pangtong", "sp_zhugeliang", "taishici", "yanwen", "re_yuansha<PERSON>", "re_pangde"],
    shenhua<PERSON>in: ["caopi", "re_xuhuang", "menghuo", "zhurong", "re_lusu", "sunjian", "dongzhuo", "jiaxu"],
    shenhua<PERSON>han: ["dengai", "zhanghe", "liushan", "jiangwei", "zhangzhang", "sunce", "caiwenji", "zuoci"]
};

// 合并所有武将
const allCharacters = [].concat(
    characterPacks.standard2008,
    characterPacks.shenhuaFeng,
    characterPacks.shenhuaHuo,
    characterPacks.shenhuaLin,
    characterPacks.shenhuaShan
);

console.log("\n=== 武将包验证 ===");
console.log(`2008版标准包: ${characterPacks.standard2008.length}个武将`);
console.log(`神话再临-风: ${characterPacks.shenhuaFeng.length}个武将`);
console.log(`神话再临-火: ${characterPacks.shenhuaHuo.length}个武将`);
console.log(`神话再临-林: ${characterPacks.shenhuaLin.length}个武将`);
console.log(`神话再临-山: ${characterPacks.shenhuaShan.length}个武将`);
console.log(`总计: ${allCharacters.length}个武将`);

// 验证武将数量是否足够6人游戏
const requiredCharacters = 6 * 6; // 6人每人6张武将
console.log(`\n=== 数量验证 ===`);
console.log(`需要武将数量: ${requiredCharacters}个`);
console.log(`可用武将数量: ${allCharacters.length}个`);
console.log(`是否足够: ${allCharacters.length >= requiredCharacters ? '✅ 是' : '❌ 否'}`);

// 检查是否有重复武将
const uniqueCharacters = [...new Set(allCharacters)];
console.log(`\n=== 重复检查 ===`);
console.log(`原始武将数量: ${allCharacters.length}`);
console.log(`去重后数量: ${uniqueCharacters.length}`);
console.log(`是否有重复: ${allCharacters.length !== uniqueCharacters.length ? '⚠️ 是' : '✅ 否'}`);

if (allCharacters.length !== uniqueCharacters.length) {
    const duplicates = allCharacters.filter((item, index) => allCharacters.indexOf(item) !== index);
    console.log(`重复的武将: ${[...new Set(duplicates)].join(', ')}`);
}

// 模拟实际游戏分配
console.log(`\n=== 游戏分配模拟 ===`);
function simulateGameDistribution() {
    const shuffled = [...allCharacters].sort(() => Math.random() - 0.5);
    const players = [];
    
    for (let i = 0; i < 6; i++) {
        const playerCharacters = shuffled.slice(i * 6, (i + 1) * 6);
        players.push({
            id: i,
            characters: playerCharacters
        });
    }
    
    return players;
}

const simulation = simulateGameDistribution();
simulation.forEach((player, index) => {
    console.log(`Player${index}: ${player.characters.length}个武将`);
});

// 验证武将包分布
console.log(`\n=== 武将包分布验证 ===`);
function getCharacterPack(character) {
    if (characterPacks.standard2008.includes(character)) return 'standard2008';
    if (characterPacks.shenhuaFeng.includes(character)) return 'shenhuaFeng';
    if (characterPacks.shenhuaHuo.includes(character)) return 'shenhuaHuo';
    if (characterPacks.shenhuaLin.includes(character)) return 'shenhuaLin';
    if (characterPacks.shenhuaShan.includes(character)) return 'shenhuaShan';
    return 'unknown';
}

const packDistribution = {
    standard2008: 0,
    shenhuaFeng: 0,
    shenhuaHuo: 0,
    shenhuaLin: 0,
    shenhuaShan: 0,
    unknown: 0
};

allCharacters.forEach(char => {
    const pack = getCharacterPack(char);
    packDistribution[pack]++;
});

console.log('武将包分布:');
console.log(`- 2008版标准包: ${packDistribution.standard2008}个`);
console.log(`- 神话再临-风: ${packDistribution.shenhuaFeng}个`);
console.log(`- 神话再临-火: ${packDistribution.shenhuaHuo}个`);
console.log(`- 神话再临-林: ${packDistribution.shenhuaLin}个`);
console.log(`- 神话再临-山: ${packDistribution.shenhuaShan}个`);
if (packDistribution.unknown > 0) {
    console.log(`- 未知包: ${packDistribution.unknown}个`);
}

// 验证代码一致性
console.log(`\n=== 代码一致性验证 ===`);
const codeSnippet = `
// 游戏代码中的武将列表应该与此完全一致
var standard2008 = ["caocao", "simayi", "xiahoudun", "zhangliao", "xuzhu", "guojia", "zhenji", "liubei", "guanyu", "zhangfei", "zhugeliang", "zhaoyun", "machao", "huangyueying", "sunquan", "ganning", "lvmeng", "huanggai", "zhouyu", "daqiao", "luxun", "sunshangxiang", "huatuo", "lvbu", "diaochan"];
var shenhuaFeng = ["sp_zhangjiao", "re_yuji", "old_zhoutai", "old_caoren", "re_xiahouyuan", "xiaoqiao", "re_huangzhong", "re_weiyan"];
var shenhuaHuo = ["dianwei", "xunyu", "pangtong", "sp_zhugeliang", "taishici", "yanwen", "re_yuanshao", "re_pangde"];
var shenhuaLin = ["caopi", "re_xuhuang", "menghuo", "zhurong", "re_lusu", "sunjian", "dongzhuo", "jiaxu"];
var shenhuaShan = ["dengai", "zhanghe", "liushan", "jiangwei", "zhangzhang", "sunce", "caiwenji", "zuoci"];
`;

console.log('✅ 武将列表已验证，可直接复制到游戏代码中使用');

// 最终验证结果
console.log(`\n=== 最终验证结果 ===`);
const checks = [
    { name: '武将数量充足', pass: allCharacters.length >= 36 },
    { name: '无重复武将', pass: allCharacters.length === uniqueCharacters.length },
    { name: '包含2008版标准包', pass: characterPacks.standard2008.length === 25 },
    { name: '包含神话再临风包', pass: characterPacks.shenhuaFeng.length === 8 },
    { name: '包含神话再临火包', pass: characterPacks.shenhuaHuo.length === 8 },
    { name: '包含神话再临林包', pass: characterPacks.shenhuaLin.length === 8 },
    { name: '包含神话再临山包', pass: characterPacks.shenhuaShan.length === 8 },
    { name: '总数正确', pass: allCharacters.length === 57 }
];

let passedChecks = 0;
checks.forEach(check => {
    const status = check.pass ? '✅' : '❌';
    console.log(`${status} ${check.name}`);
    if (check.pass) passedChecks++;
});

console.log(`\n通过检查: ${passedChecks}/${checks.length}`);
if (passedChecks === checks.length) {
    console.log('🎉 所有验证通过！武将选择配置完全正确！');
} else {
    console.log('⚠️ 部分验证失败，需要检查配置。');
}

console.log('\n=== 验证完成 ===');
