// 暗身份双将3V3模式游戏结束判断测试
console.log("=== 暗身份双将3V3模式游戏结束判断测试 ===");

// 模拟游戏状态
const gameState = {
    mode: "hidden_double_3v3",
    players: [],
    zhu: null,
    nei: null
};

// 创建6个玩家
const identities = ["zhu", "zhong", "zhong", "nei", "fan", "fan"];
for (let i = 0; i < 6; i++) {
    const player = {
        id: i,
        name: `Player${i}`,
        identity: identities[i],
        isDead: false,
        isAlive: function() { return !this.isDead; },
        die: function() { 
            this.isDead = true;
            console.log(`${this.name}(${this.identity}) 死亡`);
            checkGameResult();
        }
    };
    
    gameState.players.push(player);
    
    if (player.identity === "zhu") {
        gameState.zhu = player;
    } else if (player.identity === "nei") {
        gameState.nei = player;
    }
}

// 游戏结束判断函数
function checkGameResult() {
    console.log("\n--- 检查游戏结果 ---");
    
    const zhu = gameState.zhu;
    const nei = gameState.nei;
    
    // 主公或内奸死亡时游戏结束
    if (zhu && zhu.isDead) {
        console.log("🎯 主公死亡，游戏结束！");
        console.log("🏆 反贼势力(内奸+反贼)获胜");
        showFinalResult("rebel");
        return true;
    }
    
    if (nei && nei.isDead) {
        console.log("🎯 内奸死亡，游戏结束！");
        console.log("🏆 忠臣势力(主公+忠臣)获胜");
        showFinalResult("loyal");
        return true;
    }
    
    console.log("⏳ 游戏继续...");
    return false;
}

// 显示最终结果
function showFinalResult(winner) {
    console.log("\n=== 游戏结束 ===");
    console.log("最终身份分配:");
    
    const loyalGroup = gameState.players.filter(p => p.identity === "zhu" || p.identity === "zhong");
    const rebelGroup = gameState.players.filter(p => p.identity === "nei" || p.identity === "fan");
    
    console.log("\n忠臣势力 (主公+忠臣):");
    loyalGroup.forEach(p => {
        const status = p.isDead ? "💀" : "❤️";
        console.log(`  ${status} ${p.name}: ${p.identity}`);
    });
    
    console.log("\n反贼势力 (内奸+反贼):");
    rebelGroup.forEach(p => {
        const status = p.isDead ? "💀" : "❤️";
        console.log(`  ${status} ${p.name}: ${p.identity}`);
    });
    
    if (winner === "loyal") {
        console.log("\n🎉 忠臣势力获胜！");
    } else {
        console.log("\n🎉 反贼势力获胜！");
    }
}

// 显示初始状态
console.log("\n=== 游戏开始状态 ===");
console.log("玩家身份分配:");
gameState.players.forEach(p => {
    console.log(`${p.name}: ${p.identity}`);
});

console.log(`\n关键角色:`);
console.log(`主公: ${gameState.zhu.name}`);
console.log(`内奸: ${gameState.nei.name}`);

// 测试场景1：主公死亡
console.log("\n=== 测试场景1：主公死亡 ===");
gameState.zhu.die();

// 重置游戏状态
gameState.zhu.isDead = false;

// 测试场景2：内奸死亡
console.log("\n=== 测试场景2：内奸死亡 ===");
gameState.nei.die();

// 重置游戏状态
gameState.nei.isDead = false;

// 测试场景3：其他角色死亡（游戏应该继续）
console.log("\n=== 测试场景3：忠臣死亡（游戏继续） ===");
const zhong = gameState.players.find(p => p.identity === "zhong");
zhong.die();

console.log("\n=== 测试场景4：反贼死亡（游戏继续） ===");
const fan = gameState.players.find(p => p.identity === "fan");
fan.die();

console.log("\n=== 测试完成 ===");
console.log("✅ 游戏结束判断逻辑正确：");
console.log("  - 主公死亡时游戏立即结束，反贼势力获胜");
console.log("  - 内奸死亡时游戏立即结束，忠臣势力获胜");
console.log("  - 其他角色死亡时游戏继续");
