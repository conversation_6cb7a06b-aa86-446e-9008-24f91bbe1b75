const characterSort = {
	tw_sp: ["tw_simashi", "tw_beimihu", "tw_qiaozhou", "old_jiakui", "tw_jsp_guanyu", "tw_mazhong", "licuil<PERSON><PERSON><PERSON>quanding", "simafu", "tw_zhu<PERSON><PERSON>", "tw_yanliang", "tw_wenchou", "tw_yuantan", "tw_zhang<PERSON><PERSON>", "tw_zhang<PERSON>", "tw_fuwan", "tw_yujin", "tw_zhaoxiang", "tw_hucheer", "tw_hejin", "tw_mayunlu", "tw_re_caohong", "tw_zangba", "tw_liuhong", "tw_tianyu", "jiachong", "duosidawang", "wuban", "yuejiu", "tw_caocao", "tw_zhang<PERSON><PERSON>", "tw_caozhao", "tw_wangchang", "tw_puyangxing", "tw_jiangji", "tw_niujin", "tw_x<PERSON><PERSON><PERSON>", "tw_xiah<PERSON><PERSON>", "tw_zhangji", "tw_zhangnan", "tw_fengxí", "tw_furong", "tw_liwei", "tw_yangyi", "tw_daxiaoqiao", "tw_dengzhi", "tw_baoxin", "tw_bingyuan", "tw_fanchou", "tw_haomeng", "tw_huchuquan", "tw_jianshuo", "tw_jiling", "tw_liufuren", "tw_mateng", "tw_niufudongxie", "tw_qiaorui", "tw_weixu", "tw_yanxiang", "tw_yufuluo", "tw_zhangning", "tw_dengzhi", "tw_yangyi", "tw_yangang", "tw_gongsunfan"],
	tw_yunchouzhi: ["tw_wangcan", "tw_dongzhao", "tw_bianfuren", "tw_feiyi", "tw_chenzhen", "tw_xunchen"],
	tw_yunchouxin: ["tw_wangling", "tw_huojun", "tw_wujing", "tw_zhouchu"],
	tw_yunchouren: ["tw_liuzhang", "tw_xujing", "tw_qiaogong"],
	tw_yunchouyong: ["tw_zongyu", "tw_chendong", "tw_sunyi"],
	tw_yunchouyan: ["tw_jiangqing"],
	tw_sbcharacter: ["tw_sb_sp_zhugeliang", "tw_sb_caopi"],
	tw_swordsman: ["xia_yuzhenzi", "xia_shie", "xia_shitao", "xia_guanyu", "xia_liubei", "xia_xiahousone", "xia_xiahoudun", "xia_zhangwei", "xia_xushu", "xia_wangyue", "xia_liyàn", "xia_tongyuan", "xia_lusu", "xia_dianwei", "xia_zhaoe", "xia_xiahouzie"],
	tw_beiding: ["huan_luxun", "huan_liushan", "huan_zhugeliang", "huan_jiangwei", "huan_zhanghe", "huan_zhugeguo", "huan_weiyan", "huan_simayi", "huan_zhaoyun"],
	tw_weiang: ["huan_liufeng", "huan_caoang", "huan_huanggai", "huan_dingshangwan", "huan_dianwei", "huan_caopi", "huan_caozhi", "huan_caochong"],
	tw_mobile: ["nashime", "tw_gexuan", "tw_zhugeguo", "tw_yj_zhanghe"],
	tw_standard: ["tw_zhangfei"],
	tw_shenhua_yin: ["tw_yl_luzhi"],
	tw_shenhua_lei: ["tw_guanqiujian"],
	tw_yijiang1: ["tw_re_fazheng"],
	tw_yijiang2: ["tw_chengpu", "tw_madai", "tw_handang"],
	tw_yijiang3: ["tw_fuhuanghou", "tw_guohuai"],
	tw_yijiang4: ["tw_sunluban", "tw_guyong"],
	tw_yijiang5: ["tw_caoxiu", "old_quancong"],
	tw_yijiang7: ["tw_xuezong"],
	tw_yijiang: ["tw_caoang", "tw_caohong", "tw_zumao", "tw_dingfeng", "tw_maliang", "tw_xiahouba"],
	tw_english: ["kaisa"],
	tw_waitingforsort: ["tw_zhangyun"],
};

const characterSortTranslate = {
	tw_sp: "海外服·SP",
	tw_mobile: "海外服·稀有专属",
	tw_yunchouzhi: "运筹帷幄·智",
	tw_yunchouxin: "运筹帷幄·信",
	tw_yunchouren: "运筹帷幄·仁",
	tw_yunchouyong: "运筹帷幄·勇",
	tw_yunchouyan: "运筹帷幄·严",
	tw_sbcharacter: "海外服·谋攻篇",
	tw_swordsman: "海外服·武侠篇",
	tw_beiding: "海外服·北定中原",
	tw_weiang: "海外服·魏昂龙兴",
	tw_standard: "海外服异构·标准包",
	tw_shenhua_yin: "海外服异构·难知如阴",
	tw_shenhua_lei: "海外服异构·动如雷霆",
	tw_yijiang1: "海外服异构·将1",
	tw_yijiang2: "海外服异构·将2",
	tw_yijiang3: "海外服异构·将3",
	tw_yijiang4: "海外服异构·将4",
	tw_yijiang5: "海外服异构·将5",
	tw_yijiang7: "海外服异构·原7",
	tw_yijiang: "一将成名TW",
	tw_english: "英文版",
	tw_waitingforsort: "等待分包",
};

export { characterSort, characterSortTranslate };
