// 暗身份双将3V3模式身份显示测试
console.log("=== 暗身份双将3V3模式身份显示测试 ===");

// 模拟6人游戏的身份分配
const identityConfig = ["zhu", "zhong", "zhong", "nei", "fan", "fan"];
console.log("身份配置:", identityConfig);
console.log("身份统计:");
console.log("- 主公(zhu):", identityConfig.filter(id => id === "zhu").length);
console.log("- 忠臣(zhong):", identityConfig.filter(id => id === "zhong").length);
console.log("- 内奸(nei):", identityConfig.filter(id => id === "nei").length);
console.log("- 反贼(fan):", identityConfig.filter(id => id === "fan").length);

// 模拟玩家身份分配
function simulateIdentityAssignment() {
    const players = [];
    const shuffledIdentities = [...identityConfig].sort(() => Math.random() - 0.5);
    
    for (let i = 0; i < 6; i++) {
        players.push({
            id: i,
            name: `Player${i}`,
            identity: shuffledIdentities[i],
            identityShown: false,
            isMe: i === 0, // 假设Player0是玩家自己
            canSeeIdentity: function() {
                return this.isMe || this.identityShown;
            },
            getDisplayIdentity: function() {
                if (this.canSeeIdentity()) {
                    return this.identity;
                } else {
                    return "cai"; // 未知身份显示为"猜"
                }
            }
        });
    }
    
    return players;
}

// 测试身份显示
const players = simulateIdentityAssignment();
console.log("\n=== 游戏开始时的身份显示 ===");
players.forEach(player => {
    const displayIdentity = player.getDisplayIdentity();
    const realIdentity = player.identity;
    console.log(`${player.name}: 显示身份=${displayIdentity}, 真实身份=${realIdentity}${player.isMe ? ' (自己)' : ''}`);
});

// 模拟身份查看机制
function revealIdentity(playerIndex) {
    if (playerIndex >= 0 && playerIndex < players.length) {
        players[playerIndex].identityShown = true;
        console.log(`\n${players[playerIndex].name}的身份被揭示: ${players[playerIndex].identity}`);
    }
}

// 测试身份查看
console.log("\n=== 模拟身份查看过程 ===");
console.log("Player1的血量降至1点，身份被查看...");
revealIdentity(1);

console.log("\n=== 身份查看后的显示状态 ===");
players.forEach(player => {
    const displayIdentity = player.getDisplayIdentity();
    const realIdentity = player.identity;
    const status = player.identityShown ? '(已揭示)' : player.isMe ? '(自己)' : '(隐藏)';
    console.log(`${player.name}: 显示身份=${displayIdentity}, 真实身份=${realIdentity} ${status}`);
});

// 势力划分测试
console.log("\n=== 势力划分测试 ===");
const loyalGroup = players.filter(p => p.identity === "zhu" || p.identity === "zhong");
const rebelGroup = players.filter(p => p.identity === "nei" || p.identity === "fan");

console.log("忠臣势力 (主公+忠臣):");
loyalGroup.forEach(p => console.log(`  - ${p.name}: ${p.identity}`));

console.log("反贼势力 (内奸+反贼):");
rebelGroup.forEach(p => console.log(`  - ${p.name}: ${p.identity}`));

// 胜负条件测试
console.log("\n=== 胜负条件测试 ===");
const zhu = players.find(p => p.identity === "zhu");
const nei = players.find(p => p.identity === "nei");

console.log(`主公: ${zhu.name}`);
console.log(`内奸: ${nei.name}`);
console.log("胜负条件:");
console.log("- 主公死亡 → 反贼势力(内奸+反贼)获胜");
console.log("- 内奸死亡 → 忠臣势力(主公+忠臣)获胜");

console.log("\n=== 测试完成 ===");
