window.noname_source_list = [
	"LICENSE",
	"noname.js",
	"noname-compatible.js",
	"service-worker.js",

	"noname/ai/basic.js",
	"noname/ai/index.js",
	"noname/game/check.js",
	"noname/game/compatible.js",
	"noname/game/index.js",
	"noname/game/PauseManager.js",
	"noname/game/promises.js",
	"noname/game/dynamic-style/index.js",
	"noname/get/audio.js",
	"noname/get/compatible.js",
	"noname/get/index.js",
	"noname/get/is.js",
	"noname/get/promises.js",
	"noname/get/pinyins/index.js",
	"noname/get/pinyins/noname-dict.js",
	"noname/gnc/index.js",
	"noname/gnc/is.js",
	"noname/init/cordova.js",
	"noname/init/import.js",
	"noname/init/index.js",
	"noname/init/loading.js",
	"noname/init/node.js",
	"noname/init/onload.js",
	"noname/init/polyfill.js",
	"noname/init/onload/default-splash.js",
	"noname/init/onload/index.js",
	"noname/init/onload/OnloadSplash.js",
	"noname/init/onload/wide-splash.js",
	"noname/library/index.js",
	"noname/library/path.js",
	"noname/library/update-urls.js",
	"noname/library/update.js",
	"noname/library/announce/index.js",
	"noname/library/assembly/index.js",
	"noname/library/assembly/buildin.js",
	"noname/library/cache/cacheContext.js",
	"noname/library/cache/childNodesWatcher.js",
	"noname/library/channel/index.js",
	"noname/library/crypt/md5.js",
	"noname/library/element/GameEvent/compilers/ArrayCompiler.js",
	"noname/library/element/GameEvent/compilers/AsyncCompiler.js",
	"noname/library/element/GameEvent/compilers/ContentCompiler.js",
	"noname/library/element/GameEvent/compilers/ContentCompilerBase.js",
	"noname/library/element/GameEvent/compilers/StepCompiler.js",
	"noname/library/element/GameEvent/compilers/YieldCompiler.js",
	"noname/library/element/GameEvent/GameEventManager.js",
	"noname/library/element/button.js",
	"noname/library/element/card.js",
	"noname/library/element/character.js",
	"noname/library/element/client.js",
	"noname/library/element/content.js",
	"noname/library/element/contents.js",
	"noname/library/element/control.js",
	"noname/library/element/dialog.js",
	"noname/library/element/gameEvent.js",
	"noname/library/element/gameEventPromise.js",
	"noname/library/element/index.js",
	"noname/library/element/nodeWS.js",
	"noname/library/element/player.js",
	"noname/library/element/vcard.js",
	"noname/library/experimental/index.js",
	"noname/library/experimental/symbol.js",
	"noname/library/hooks/buildin.js",
	"noname/library/hooks/hook.js",
	"noname/library/hooks/index.js",
	"noname/library/init/index.js",
	"noname/library/init/promises.js",
	"noname/status/index.js",
	"noname/ui/index.js",
	"noname/ui/click/index.js",
	"noname/ui/create/index.js",
	"noname/ui/create/menu/index.js",
	"noname/ui/create/menu/pages/cardPackMenu.js",
	"noname/ui/create/menu/pages/characterPackMenu.js",
	"noname/ui/create/menu/pages/exetensionMenu.js",
	"noname/ui/create/menu/pages/optionsMenu.js",
	"noname/ui/create/menu/pages/otherMenu.js",
	"noname/ui/create/menu/pages/startMenu.js",
	"noname/util/browser.js",
	"noname/util/config.js",
	"noname/util/error.js",
	"noname/util/index.js",
	"noname/util/initRealms.js",
	"noname/util/mutex.js",
	"noname/util/sandbox.js",
	"noname/util/security.js",
	"noname/util/struct/index.js",
	"noname/util/struct/interface/index.d.ts",
	"noname/util/struct/interface/promise-error-handler.d.ts",
	"noname/util/struct/promise-error-handler/chrome.js",
	"noname/util/struct/promise-error-handler/firefox.js",
	"noname/util/struct/promise-error-handler/index.js",
	"noname/util/struct/promise-error-handler/unknown.js",

	"card/extra.js",
	"card/gujian.js",
	"card/guozhan.js",
	"card/gwent.js",
	"card/hearth.js",
	"card/huanlekapai.js",
	"card/mtg.js",
	"card/sp.js",
	"card/standard.js",
	"card/swd.js",
	"card/xianxia.js",
	"card/yingbian.js",
	"card/yongjian.js",
	"card/yunchou.js",
	"card/zhenfa.js",
	"card/zhulu.js",

	"character/gujian.js",
	"character/gwent.js",
	"character/hearth.js",
	"character/mtg.js",
	"character/ow.js",
	"character/rank.js",
	"character/swd.js",
	"character/xianjian.js",
	"character/yxs.js",
	"character/clan/card.js",
	"character/clan/character.js",
	"character/clan/characterFilter.js",
	"character/clan/characterReplace.js",
	"character/clan/dynamicTranslate.js",
	"character/clan/index.js",
	"character/clan/intro.js",
	"character/clan/pinyin.js",
	"character/clan/skill.js",
	"character/clan/sort.js",
	"character/clan/translate.js",
	"character/clan/voices.js",
	"character/collab/card.js",
	"character/collab/character.js",
	"character/collab/characterFilter.js",
	"character/collab/characterReplace.js",
	"character/collab/dynamicTranslate.js",
	"character/collab/index.js",
	"character/collab/intro.js",
	"character/collab/pinyin.js",
	"character/collab/skill.js",
	"character/collab/sort.js",
	"character/collab/translate.js",
	"character/collab/voices.js",
	"character/ddd/card.js",
	"character/ddd/character.js",
	"character/ddd/characterFilter.js",
	"character/ddd/characterReplace.js",
	"character/ddd/dynamicTranslate.js",
	"character/ddd/index.js",
	"character/ddd/intro.js",
	"character/ddd/pinyin.js",
	"character/ddd/skill.js",
	"character/ddd/sort.js",
	"character/ddd/translate.js",
	"character/ddd/voices.js",
	"character/diy/card.js",
	"character/diy/character.js",
	"character/diy/characterFilter.js",
	"character/diy/characterReplace.js",
	"character/diy/characterTitles.js",
	"character/diy/dynamicTranslate.js",
	"character/diy/index.js",
	"character/diy/intro.js",
	"character/diy/perfectPairs.js",
	"character/diy/pinyin.js",
	"character/diy/skill.js",
	"character/diy/sort.js",
	"character/diy/translate.js",
	"character/diy/voices.js",
	"character/extra/card.js",
	"character/extra/character.js",
	"character/extra/characterFilter.js",
	"character/extra/characterReplace.js",
	"character/extra/dynamicTranslate.js",
	"character/extra/index.js",
	"character/extra/intro.js",
	"character/extra/pinyin.js",
	"character/extra/skill.js",
	"character/extra/sort.js",
	"character/extra/translate.js",
	"character/extra/voices.js",
	"character/huicui/card.js",
	"character/huicui/character.js",
	"character/huicui/characterFilter.js",
	"character/huicui/characterReplace.js",
	"character/huicui/dynamicTranslate.js",
	"character/huicui/index.js",
	"character/huicui/intro.js",
	"character/huicui/pinyin.js",
	"character/huicui/skill.js",
	"character/huicui/sort.js",
	"character/huicui/translate.js",
	"character/huicui/voices.js",
	"character/jsrg/card.js",
	"character/jsrg/character.js",
	"character/jsrg/characterFilter.js",
	"character/jsrg/characterReplace.js",
	"character/jsrg/dynamicTranslate.js",
	"character/jsrg/index.js",
	"character/jsrg/intro.js",
	"character/jsrg/pinyin.js",
	"character/jsrg/skill.js",
	"character/jsrg/sort.js",
	"character/jsrg/translate.js",
	"character/jsrg/voices.js",
	"character/key/card.js",
	"character/key/character.js",
	"character/key/index.js",
	"character/key/pinyin.js",
	"character/key/skill.js",
	"character/key/sort.js",
	"character/key/translate.js",
	"character/key/voices.js",
	"character/mobile/card.js",
	"character/mobile/character.js",
	"character/mobile/characterFilter.js",
	"character/mobile/characterReplace.js",
	"character/mobile/dynamicTranslate.js",
	"character/mobile/index.js",
	"character/mobile/intro.js",
	"character/mobile/perfectPairs.js",
	"character/mobile/pinyin.js",
	"character/mobile/skill.js",
	"character/mobile/sort.js",
	"character/mobile/translate.js",
	"character/mobile/voices.js",
	"character/newjiang/card.js",
	"character/newjiang/character.js",
	"character/newjiang/characterFilter.js",
	"character/newjiang/characterReplace.js",
	"character/newjiang/dynamicTranslate.js",
	"character/newjiang/index.js",
	"character/newjiang/intro.js",
	"character/newjiang/perfectPairs.js",
	"character/newjiang/pinyin.js",
	"character/newjiang/skill.js",
	"character/newjiang/sort.js",
	"character/newjiang/translate.js",
	"character/newjiang/voices.js",
	"character/offline/card.js",
	"character/offline/character.js",
	"character/offline/characterFilter.js",
	"character/offline/characterReplace.js",
	"character/offline/dynamicTranslate.js",
	"character/offline/index.js",
	"character/offline/intro.js",
	"character/offline/pinyin.js",
	"character/offline/skill.js",
	"character/offline/sort.js",
	"character/offline/translate.js",
	"character/offline/voices.js",
	"character/old/card.js",
	"character/old/character.js",
	"character/old/characterFilter.js",
	"character/old/characterReplace.js",
	"character/old/dynamicTranslate.js",
	"character/old/index.js",
	"character/old/intro.js",
	"character/old/pinyin.js",
	"character/old/skill.js",
	"character/old/sort.js",
	"character/old/translate.js",
	"character/old/voices.js",
	"character/onlyOL/card.js",
	"character/onlyOL/character.js",
	"character/onlyOL/characterFilter.js",
	"character/onlyOL/characterReplace.js",
	"character/onlyOL/dynamicTranslate.js",
	"character/onlyOL/index.js",
	"character/onlyOL/intro.js",
	"character/onlyOL/pinyin.js",
	"character/onlyOL/skill.js",
	"character/onlyOL/sort.js",
	"character/onlyOL/translate.js",
	"character/onlyOL/voices.js",
	"character/refresh/card.js",
	"character/refresh/character.js",
	"character/refresh/characterFilter.js",
	"character/refresh/characterReplace.js",
	"character/refresh/dynamicTranslate.js",
	"character/refresh/index.js",
	"character/refresh/intro.js",
	"character/refresh/perfectPairs.js",
	"character/refresh/pinyin.js",
	"character/refresh/skill.js",
	"character/refresh/sort.js",
	"character/refresh/translate.js",
	"character/refresh/voices.js",
	"character/sb/card.js",
	"character/sb/character.js",
	"character/sb/characterFilter.js",
	"character/sb/characterReplace.js",
	"character/sb/dynamicTranslate.js",
	"character/sb/index.js",
	"character/sb/intro.js",
	"character/sb/pinyin.js",
	"character/sb/skill.js",
	"character/sb/sort.js",
	"character/sb/translate.js",
	"character/sb/voices.js",
	"character/shenhua/card.js",
	"character/shenhua/character.js",
	"character/shenhua/characterFilter.js",
	"character/shenhua/characterReplace.js",
	"character/shenhua/dynamicTranslate.js",
	"character/shenhua/index.js",
	"character/shenhua/intro.js",
	"character/shenhua/perfectPairs.js",
	"character/shenhua/pinyin.js",
	"character/shenhua/skill.js",
	"character/shenhua/sort.js",
	"character/shenhua/translate.js",
	"character/shenhua/voices.js",
	"character/shiji/card.js",
	"character/shiji/character.js",
	"character/shiji/characterFilter.js",
	"character/shiji/characterReplace.js",
	"character/shiji/dynamicTranslate.js",
	"character/shiji/index.js",
	"character/shiji/intro.js",
	"character/shiji/perfectPairs.js",
	"character/shiji/pinyin.js",
	"character/shiji/skill.js",
	"character/shiji/sort.js",
	"character/shiji/translate.js",
	"character/shiji/voices.js",
	"character/sixiang/card.js",
	"character/sixiang/character.js",
	"character/sixiang/characterFilter.js",
	"character/sixiang/characterReplace.js",
	"character/sixiang/dynamicTranslate.js",
	"character/sixiang/index.js",
	"character/sixiang/intro.js",
	"character/sixiang/perfectPairs.js",
	"character/sixiang/pinyin.js",
	"character/sixiang/skill.js",
	"character/sixiang/sort.js",
	"character/sixiang/translate.js",
	"character/sixiang/voices.js",
	"character/sp/card.js",
	"character/sp/character.js",
	"character/sp/characterFilter.js",
	"character/sp/characterReplace.js",
	"character/sp/dynamicTranslate.js",
	"character/sp/index.js",
	"character/sp/intro.js",
	"character/sp/perfectPairs.js",
	"character/sp/pinyin.js",
	"character/sp/skill.js",
	"character/sp/sort.js",
	"character/sp/translate.js",
	"character/sp/voices.js",
	"character/sp2/card.js",
	"character/sp2/character.js",
	"character/sp2/characterFilter.js",
	"character/sp2/characterReplace.js",
	"character/sp2/dynamicTranslate.js",
	"character/sp2/index.js",
	"character/sp2/intro.js",
	"character/sp2/perfectPairs.js",
	"character/sp2/pinyin.js",
	"character/sp2/skill.js",
	"character/sp2/sort.js",
	"character/sp2/translate.js",
	"character/sp2/voices.js",
	"character/standard/card.js",
	"character/standard/character.js",
	"character/standard/characterFilter.js",
	"character/standard/characterReplace.js",
	"character/standard/dynamicTranslate.js",
	"character/standard/index.js",
	"character/standard/intro.js",
	"character/standard/perfectPairs.js",
	"character/standard/pinyin.js",
	"character/standard/skill.js",
	"character/standard/sort.js",
	"character/standard/translate.js",
	"character/standard/voices.js",
	"character/tw/card.js",
	"character/tw/character.js",
	"character/tw/characterFilter.js",
	"character/tw/characterReplace.js",
	"character/tw/dynamicTranslate.js",
	"character/tw/index.js",
	"character/tw/intro.js",
	"character/tw/perfectPairs.js",
	"character/tw/pinyin.js",
	"character/tw/skill.js",
	"character/tw/sort.js",
	"character/tw/translate.js",
	"character/tw/voices.js",
	"character/xianding/card.js",
	"character/xianding/character.js",
	"character/xianding/characterFilter.js",
	"character/xianding/characterReplace.js",
	"character/xianding/dynamicTranslate.js",
	"character/xianding/index.js",
	"character/xianding/intro.js",
	"character/xianding/pinyin.js",
	"character/xianding/skill.js",
	"character/xianding/sort.js",
	"character/xianding/translate.js",
	"character/xianding/voices.js",
	"character/yijiang/card.js",
	"character/yijiang/character.js",
	"character/yijiang/characterFilter.js",
	"character/yijiang/characterReplace.js",
	"character/yijiang/dynamicTranslate.js",
	"character/yijiang/index.js",
	"character/yijiang/intro.js",
	"character/yijiang/perfectPairs.js",
	"character/yijiang/pinyin.js",
	"character/yijiang/skill.js",
	"character/yijiang/sort.js",
	"character/yijiang/translate.js",
	"character/yijiang/voices.js",
	"character/yingbian/card.js",
	"character/yingbian/character.js",
	"character/yingbian/characterFilter.js",
	"character/yingbian/characterReplace.js",
	"character/yingbian/dynamicTranslate.js",
	"character/yingbian/index.js",
	"character/yingbian/intro.js",
	"character/yingbian/perfectPairs.js",
	"character/yingbian/pinyin.js",
	"character/yingbian/skill.js",
	"character/yingbian/sort.js",
	"character/yingbian/translate.js",
	"character/yingbian/voices.js",

	"font/motoyamaru.woff2",
	"font/suits.woff2",

	"game/asset.js",
	"game/canUse.ts",
	"game/codemirror.js",
	"game/compiler-sfc.esm-browser.js",
	"game/config.js",
	"game/core-js-bundle.js",
	"game/directory.js",
	"game/generateChanged.js",
	"game/entry.js",
	"game/fallback.js",
	"game/game.js",
	"game/http.js",
	"game/jszip.js",
	"game/keyWords.js",
	"game/NoSleep.js",
	"game/package.js",
	"game/phantom.js",
	"game/pressure.js",
	"game/server.js",
	"game/source.js",
	"game/typescript.js",
	"game/update.js",
	"game/vue.esm-browser.js",

	"image/card/cardtempname_bg.png",
	"image/flappybird/BG.png",
	"image/flappybird/botpipe.png",
	"image/flappybird/gameclear.png",
	"image/flappybird/gameover.png",
	"image/flappybird/getready.png",
	"image/flappybird/ground.png",
	"image/flappybird/toppipe.png",
	"image/flappybird/bird/b0.png",
	"image/flappybird/bird/b1.png",
	"image/flappybird/bird/b2.png",
	"image/flappybird/ground/g0.png",
	"image/flappybird/ground/g1.png",
	"image/flappybird/tap/t0.png",
	"image/flappybird/tap/t1.png",

	"layout/default/codemirror.css",
	"layout/default/layout.css",
	"layout/default/menu.css",
	"layout/default/phone.css",
	"layout/default/toast.css",
	"layout/long/layout.css",
	"layout/long2/layout.css",
	"layout/mobile/equip.css",
	"layout/mobile/layout.css",
	"layout/mode/boss.css",
	"layout/mode/chess.css",
	"layout/mode/stone.css",
	"layout/mode/tafang.css",
	"layout/newlayout/equip.css",
	"layout/newlayout/global.css",
	"layout/newlayout/layout.css",
	"layout/nova/layout.css",

	"mode/boss.js",
	"mode/chess.js",
	"mode/doudizhu.js",
	"mode/guozhan.js",
	"mode/identity.js",
	"mode/tafang.js",
	"mode/single.js",
	"mode/stone.js",
	"mode/brawl.js",
	"mode/versus.js",
	"mode/connect.js",

	"extension/boss/extension.js",
	"extension/cardpile/extension.js",
	"extension/coin/extension.js",
	"extension/wuxing/extension.js",

	"theme/music/grid.png",
	"theme/music/style.css",
	"theme/music/wood.png",
	"theme/music/wood3.png",
	"theme/simple/card.png",
	"theme/simple/grid.png",
	"theme/simple/style.css",
	"theme/simple/unknown.png",
	"theme/simple/wood.png",
	"theme/simple/wood3.png",
	"theme/woodden/grid.png",
	"theme/woodden/style.css",
	"theme/woodden/wood.jpg",
	"theme/woodden/wood.png",
	"theme/woodden/wood2.jpg",
	"theme/woodden/wood2.png",
	"theme/style/card/custom.css",
	"theme/style/card/default.css",
	"theme/style/card/music.css",
	"theme/style/card/simple.css",
	"theme/style/card/wood.css",
	"theme/style/card/new.css",
	"theme/style/card/ol.css",
	"theme/style/card/image/new.png",
	"theme/style/card/image/ol.png",
	"theme/style/cardback/custom.css",
	"theme/style/cardback/default.css",
	"theme/style/cardback/feicheng.css",
	"theme/style/cardback/liusha.css",
	"theme/style/cardback/music.css",
	"theme/style/cardback/new.css",
	"theme/style/cardback/ol.css",
	"theme/style/cardback/official.css",
	"theme/style/cardback/wood.css",
	"theme/style/cardback/image/feicheng.png",
	"theme/style/cardback/image/feicheng2.png",
	"theme/style/cardback/image/liusha.png",
	"theme/style/cardback/image/liusha2.png",
	"theme/style/cardback/image/new.png",
	"theme/style/cardback/image/new2.png",
	"theme/style/cardback/image/official.png",
	"theme/style/cardback/image/official2.png",
	"theme/style/cardback/image/ol.png",
	"theme/style/cardback/image/ol2.png",
	"theme/style/hp/custom.css",
	"theme/style/hp/default.css",
	"theme/style/hp/emotion.css",
	"theme/style/hp/glass.css",
	"theme/style/hp/official.css",
	"theme/style/hp/ol.css",
	"theme/style/hp/round.css",
	"theme/style/hp/xinglass.css",
	"theme/style/hp/xinround.css",
	"theme/style/hp/image/emotion1.png",
	"theme/style/hp/image/emotion2.png",
	"theme/style/hp/image/emotion3.png",
	"theme/style/hp/image/emotion4.png",
	"theme/style/hp/image/glass1.png",
	"theme/style/hp/image/glass2.png",
	"theme/style/hp/image/glass3.png",
	"theme/style/hp/image/glass4.png",
	"theme/style/hp/image/hidden_hp.png",
	"theme/style/hp/image/official1.png",
	"theme/style/hp/image/official2.png",
	"theme/style/hp/image/official3.png",
	"theme/style/hp/image/official4.png",
	"theme/style/hp/image/ol1.png",
	"theme/style/hp/image/ol2.png",
	"theme/style/hp/image/ol3.png",
	"theme/style/hp/image/ol4.png",
	"theme/style/hp/image/round1.png",
	"theme/style/hp/image/round2.png",
	"theme/style/hp/image/round3.png",
	"theme/style/hp/image/round4.png",
	"theme/style/hp/image/shield.png",
	"theme/style/hp/image/xinglass1.png",
	"theme/style/hp/image/xinglass2.png",
	"theme/style/hp/image/xinglass3.png",
	"theme/style/hp/image/xinglass4.png",
	"theme/style/hp/image/xinround1.png",
	"theme/style/hp/image/xinround2.png",
	"theme/style/hp/image/xinround3.png",
	"theme/style/hp/image/xinround4.png",

	"node_modules/options/.npmignore",
	"node_modules/options/lib/options.js",
	"node_modules/options/package.json",
	"node_modules/ultron/.npmignore",
	"node_modules/ultron/.travis.yml",
	"node_modules/ultron/index.js",
	"node_modules/ultron/package.json",
	"node_modules/ultron/test.js",
	"node_modules/ws/.npmignore",
	"node_modules/ws/.travis.yml",
	"node_modules/ws/index.js",
	"node_modules/ws/lib/BufferPool.js",
	"node_modules/ws/lib/BufferUtil.fallback.js",
	"node_modules/ws/lib/BufferUtil.js",
	"node_modules/ws/lib/ErrorCodes.js",
	"node_modules/ws/lib/Extensions.js",
	"node_modules/ws/lib/PerMessageDeflate.js",
	"node_modules/ws/lib/Receiver.hixie.js",
	"node_modules/ws/lib/Receiver.js",
	"node_modules/ws/lib/Sender.hixie.js",
	"node_modules/ws/lib/Sender.js",
	"node_modules/ws/lib/Validation.fallback.js",
	"node_modules/ws/lib/Validation.js",
	"node_modules/ws/lib/WebSocket.js",
	"node_modules/ws/lib/WebSocketServer.js",
	"node_modules/ws/package.json",
];
