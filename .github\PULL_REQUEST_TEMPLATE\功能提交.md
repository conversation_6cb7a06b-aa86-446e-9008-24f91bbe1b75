<!-- 在提交PR之前，请确保检查清单框都经过了检查 -->

### PR受影响的平台
<!-- PR的代码内容涉及到哪些客户端? 浏览器端，电脑端(win, mac), 手机端(android, ios, other)或者是所有平台 -->
<!-- 如果是通用的代码，填写无即可。只需要涉及到某个平台才需要填写 -->


### 诱因和背景
<!-- 为什么需要进行此更改？它解决了什么问题？ -->
<!-- 如果它修复了一个未解决的issue，请在此处链接到该issue。 -->



### PR描述
<!-- 详细描述您的更改 -->



### PR测试
<!-- 请详细描述您是如何测试PR中更改的代码的？ -->



### 扩展适配
<!-- 如果此PR需要相当一部分扩展进行跟进或者需要UI扩展更新，请在此写出扩展跟进代码 -->



### 检查清单
<!-- 请在`[]`中加一个`x`来勾选方框且周围没有空格，如下所示：`[x]`。注意其中没有空格 -->
- [ ] 我没有把该PR提交到`master`分支
- [ ] commit中没有无用信息，和没有具体内容的“bugfix”
- [ ] 我已经进行了充足的测试，且现有的测试都已通过
- [ ] 若我拥有PR标签权限，则已确保为该PR打上标签；若我未拥有PR标签权限且该PR仍需继续提交内容，则已确保为该PR名称打上`WIP`直到本PR内容全部提交
- [ ] 如果此次PR中添加了新的武将，则我已在`character/rank.js`中添加对应的武将强度评级，并对双人武将/复姓武将添加`name:xxx`的参数
- [ ] 如果此次PR中添加了新的语音文件，则我已在`lib.translate`中加入语音文件的文字台词
- [ ] 如果此次PR涉及到新功能的添加，我已在`PR描述`中写入详细文档
- [ ] 如果此次PR需要扩展跟进，我已在`扩展适配`中写入详细文档
- [ ] 如果这个PR解决了一个issue，我在`诱因和背景`中明确链接到该issue
- [ ] 我保证该PR中没有随意修改换行符等内容，没有制造出大量的Diff
- [ ] 我保证该PR遵循项目中`.editorconfig`、`eslint.config.mjs`和`prettier.config.mjs`所规定的代码样式，并且已经通过`prettier`格式化过代码
