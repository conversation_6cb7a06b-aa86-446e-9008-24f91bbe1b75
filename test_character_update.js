// 暗身份双将3V3模式武将列表更新验证
console.log("=== 暗身份双将3V3模式武将列表更新验证 ===");

// 原始武将列表
const originalCharacters = {
    standard2008: ["caocao", "simayi", "x<PERSON><PERSON><PERSON>", "zhang<PERSON>o", "xuzhu", "guojia", "zhenji", "liubei", "guanyu", "zhangfei", "zhugeliang", "zhaoyun", "machao", "huangyueying", "sunquan", "ganning", "lvmeng", "huanggai", "zhouyu", "daqiao", "luxun", "sunshangxiang", "huatuo", "lvbu", "diaochan"],
    shenhua<PERSON><PERSON>: ["sp_zhangjiao", "re_yuji", "old_zhoutai", "old_caoren", "re_x<PERSON><PERSON><PERSON>", "xiaoqi<PERSON>", "re_huangzhong", "re_weiyan"],
    shenh<PERSON><PERSON>uo: ["dianwei", "xunyu", "pangtong", "sp_zhugeliang", "taishici", "yanwen", "re_yuanshao", "re_pangde"],
    shenh<PERSON><PERSON>in: ["caopi", "re_xuhuang", "menghuo", "zhurong", "re_lusu", "sunjian", "dongzhuo", "jiaxu"],
    shenhuaShan: ["dengai", "zhanghe", "liushan", "jiangwei", "zhangzhang", "sunce", "caiwenji", "zuoci"]
};

// 更新后的武将列表
const updatedCharacters = {
    standard2008: ["caocao", "simayi", "xiahoudun", "zhangliao", "xuzhu", "guojia", "zhenji", "liubei", "guanyu", "zhangfei", "zhaoyun", "machao", "huangyueying", "sunquan", "ganning", "lvmeng", "zhouyu", "daqiao", "luxun", "sunshangxiang", "lvbu", "diaochan"],
    shenhuaFeng: ["sp_zhangjiao", "old_zhoutai", "old_caoren", "re_xiahouyuan", "xiaoqiao", "re_huangzhong", "re_weiyan"],
    shenhuaHuo: ["dianwei", "xunyu", "pangtong", "sp_zhugeliang", "taishici", "yanwen", "re_yuanshao", "re_pangde"],
    shenhuaLin: ["caopi", "re_xuhuang", "menghuo", "zhurong", "re_lusu", "sunjian", "dongzhuo", "jiaxu"],
    shenhuaShan: ["dengai", "zhanghe", "liushan", "jiangwei", "zhangzhang", "sunce", "caiwenji", "zuoci"],
    extraCharacters: ["re_yuanshu", "yangxiu"]
};

// 需要移除的武将
const removedCharacters = ["zhugeliang", "huanggai", "re_yuji", "huatuo"];

// 需要添加的武将
const addedCharacters = ["re_yuanshu", "yangxiu"];

// 武将中文名称映射
const characterNames = {
    // 移除的武将
    zhugeliang: "卧龙诸葛亮",
    huanggai: "黄盖",
    re_yuji: "于吉",
    huatuo: "华佗",
    
    // 添加的武将
    re_yuanshu: "SP袁术",
    yangxiu: "杨修",
    
    // 其他武将（用于显示）
    caocao: "曹操", simayi: "司马懿", xiahoudun: "夏侯惇", zhangliao: "张辽",
    xuzhu: "许褚", guojia: "郭嘉", zhenji: "甄姬", liubei: "刘备",
    guanyu: "关羽", zhangfei: "张飞", zhaoyun: "赵云", machao: "马超",
    huangyueying: "黄月英", sunquan: "孙权", ganning: "甘宁", lvmeng: "吕蒙",
    zhouyu: "周瑜", daqiao: "大乔", luxun: "陆逊", sunshangxiang: "孙尚香",
    lvbu: "吕布", diaochan: "貂蝉"
};

console.log("\n=== 武将移除验证 ===");
console.log("需要移除的武将:");
removedCharacters.forEach(char => {
    console.log(`- ${characterNames[char]} (${char})`);
});

console.log("\n=== 武将添加验证 ===");
console.log("需要添加的武将:");
addedCharacters.forEach(char => {
    console.log(`- ${characterNames[char]} (${char})`);
});

// 验证移除操作
console.log("\n=== 移除操作验证 ===");
const originalTotal = [].concat(
    originalCharacters.standard2008,
    originalCharacters.shenhuaFeng,
    originalCharacters.shenhuaHuo,
    originalCharacters.shenhuaLin,
    originalCharacters.shenhuaShan
);

const updatedTotal = [].concat(
    updatedCharacters.standard2008,
    updatedCharacters.shenhuaFeng,
    updatedCharacters.shenhuaHuo,
    updatedCharacters.shenhuaLin,
    updatedCharacters.shenhuaShan,
    updatedCharacters.extraCharacters
);

removedCharacters.forEach(char => {
    const wasInOriginal = originalTotal.includes(char);
    const isInUpdated = updatedTotal.includes(char);
    const status = wasInOriginal && !isInUpdated ? '✅' : '❌';
    console.log(`${status} ${characterNames[char]}: 原有=${wasInOriginal}, 现有=${isInUpdated}`);
});

// 验证添加操作
console.log("\n=== 添加操作验证 ===");
addedCharacters.forEach(char => {
    const wasInOriginal = originalTotal.includes(char);
    const isInUpdated = updatedTotal.includes(char);
    const status = !wasInOriginal && isInUpdated ? '✅' : '❌';
    console.log(`${status} ${characterNames[char]}: 原有=${wasInOriginal}, 现有=${isInUpdated}`);
});

// 统计数量变化
console.log("\n=== 数量统计 ===");
console.log(`原始武将总数: ${originalTotal.length}`);
console.log(`更新后武将总数: ${updatedTotal.length}`);
console.log(`变化: ${updatedTotal.length - originalTotal.length} (移除4个，添加2个，净减少2个)`);

// 各包数量统计
console.log("\n=== 各包数量变化 ===");
const packChanges = [
    { name: "2008版标准包", original: originalCharacters.standard2008.length, updated: updatedCharacters.standard2008.length },
    { name: "神话再临-风", original: originalCharacters.shenhuaFeng.length, updated: updatedCharacters.shenhuaFeng.length },
    { name: "神话再临-火", original: originalCharacters.shenhuaHuo.length, updated: updatedCharacters.shenhuaHuo.length },
    { name: "神话再临-林", original: originalCharacters.shenhuaLin.length, updated: updatedCharacters.shenhuaLin.length },
    { name: "神话再临-山", original: originalCharacters.shenhuaShan.length, updated: updatedCharacters.shenhuaShan.length },
    { name: "额外武将", original: 0, updated: updatedCharacters.extraCharacters.length }
];

packChanges.forEach(pack => {
    const change = pack.updated - pack.original;
    const changeStr = change > 0 ? `+${change}` : change < 0 ? `${change}` : '0';
    console.log(`${pack.name}: ${pack.original} → ${pack.updated} (${changeStr})`);
});

// 验证6人游戏是否仍然可行
console.log("\n=== 游戏可行性验证 ===");
const requiredCharacters = 6 * 6; // 6人每人6张武将
console.log(`需要武将数量: ${requiredCharacters}`);
console.log(`可用武将数量: ${updatedTotal.length}`);
console.log(`是否足够: ${updatedTotal.length >= requiredCharacters ? '✅ 是' : '❌ 否'}`);

// 显示更新后的完整武将列表
console.log("\n=== 更新后的完整武将列表 ===");
console.log("2008版标准包 (22个):");
updatedCharacters.standard2008.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n神话再临-风 (7个):");
updatedCharacters.shenhuaFeng.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n神话再临-火 (8个):");
updatedCharacters.shenhuaHuo.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n神话再临-林 (8个):");
updatedCharacters.shenhuaLin.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n神话再临-山 (8个):");
updatedCharacters.shenhuaShan.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

console.log("\n额外武将 (2个):");
updatedCharacters.extraCharacters.forEach(char => {
    console.log(`  - ${characterNames[char] || char}`);
});

// 最终验证结果
console.log("\n=== 最终验证结果 ===");
const checks = [
    { name: '移除卧龙诸葛亮', pass: !updatedTotal.includes('zhugeliang') },
    { name: '移除黄盖', pass: !updatedTotal.includes('huanggai') },
    { name: '移除于吉', pass: !updatedTotal.includes('re_yuji') },
    { name: '移除华佗', pass: !updatedTotal.includes('huatuo') },
    { name: '添加SP袁术', pass: updatedTotal.includes('re_yuanshu') },
    { name: '添加杨修', pass: updatedTotal.includes('yangxiu') },
    { name: '武将数量足够', pass: updatedTotal.length >= 36 },
    { name: '总数正确', pass: updatedTotal.length === 55 }
];

let passedChecks = 0;
checks.forEach(check => {
    const status = check.pass ? '✅' : '❌';
    console.log(`${status} ${check.name}`);
    if (check.pass) passedChecks++;
});

console.log(`\n通过检查: ${passedChecks}/${checks.length}`);
if (passedChecks === checks.length) {
    console.log('🎉 所有验证通过！武将列表更新成功！');
} else {
    console.log('⚠️ 部分验证失败，需要检查配置。');
}

console.log('\n=== 验证完成 ===');
