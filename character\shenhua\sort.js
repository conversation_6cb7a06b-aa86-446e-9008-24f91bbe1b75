const characterSort = {
	shenhua_feng: ["sp_zhang<PERSON><PERSON>", "re_yuji", "old_zhoutai", "old_caoren", "re_x<PERSON><PERSON><PERSON>", "xia<PERSON><PERSON><PERSON>", "re_huangzhong", "re_weiyan"],
	shenhua_huo: ["dianwei", "xunyu", "pangtong", "sp_zhu<PERSON>iang", "taishici", "yanwen", "re_yuan<PERSON><PERSON>", "re_pangde"],
	shenhua_lin: ["caopi", "re_xuh<PERSON>", "menghuo", "zhurong", "re_lusu", "sunjian", "dongzhuo", "jiaxu"],
	shenhua_shan: ["dengai", "zhanghe", "liushan", "jiangwei", "zhangzhang", "sunce", "caiwenji", "zuoci"],
	shenhua_yin: ["wangji", "kuailiang<PERSON><PERSON>yue", "yanyan", "wangping", "sunliang", "luji", "xuy<PERSON>", "yl_luzhi"],
	shenhua_lei: ["haozhao", "guanqi<PERSON><PERSON><PERSON>", "chendao", "zhugezhan", "lukang", "zhou<PERSON><PERSON>", "zhangxiu", "yl_yuanshu"],
};

const characterSortTranslate = {
	shenhua_feng: "神话再临·风",
	shenhua_huo: "神话再临·火",
	shenhua_lin: "神话再临·林",
	shenhua_shan: "神话再临·山",
	shenhua_yin: "神话再临·阴",
	shenhua_lei: "神话再临·雷",
};

export { characterSort, characterSortTranslate };
