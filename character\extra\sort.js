const characterSort = {
	extra_feng: ["shen_guanyu", "shen_lvmeng"],
	extra_huo: ["shen_zhu<PERSON>iang", "shen_zhouyu"],
	extra_lin: ["shen_caocao", "shen_lvbu"],
	extra_shan: ["shen_z<PERSON><PERSON>", "shen_simayi"],
	extra_yin: ["shen_liubei", "shen_luxun"],
	extra_lei: ["shen_ganning", "shen_zhang<PERSON>o"],
	extra_decade: ["shen_zhong<PERSON>", "shen_huangzhong", "shen_jiangwei", "shen_machao", "shen_zhang<PERSON>i", "shen_zhang<PERSON><PERSON>", "shen_dengai", "shen_xuzhu", "dc_shen_huatuo", "shen_pangtong"],
	extra_ol: ["shen_dianwei", "ol_zhang<PERSON>o", "shen_caopi", "shen_zhenji", "shen_sunquan", "junk_sunquan", "junk_zhangjiao"],
	extra_mobilezhi: ["shen_guojia", "shen_xunyu"],
	extra_mobilexin: ["shen_taishici", "shen_sunce"],
	extra_mobileren: ["shen_huatuo", "shen_lusu"],
	extra_tw: ["tw_shen_guanyu", "tw_shen_lvmeng"],
	extra_mb: ["xin_simayi", "new_simayi"],
	extra_offline: ["shen_jiaxu", "shen_diaochan", "boss_zhaoyun", "le_shen_jiaxu", "ps_shen_machao"],
	extra_hanmo: ["hm_shen_luzhi", "hm_shen_huangfusong", "hm_shen_zhangjiao", "hm_shen_zhangbao", "hm_shen_zhangliang", "hm_shen_zhujun"],
	extra_changan: ["ca_shen_wangyun", "ca_shen_caocao", "ca_shen_lijueguosi", "zombie_jiaxu", "zombie_zombie"],
	extra_taoyuan: ["ty_shen_zhangfei", "ty_shen_guanyu", "ty_shen_liubei"],
	extra_jingxiang: ["jx_shen_caoren", "jx_shen_liubiao"],
};

const characterSortTranslate = {
	extra_feng: "神话再临·风",
	extra_huo: "神话再临·火",
	extra_lin: "神话再临·林",
	extra_shan: "神话再临·山",
	extra_yin: "神话再临·阴",
	extra_lei: "神话再临·雷",
	extra_key: "论外",
	extra_ol: "神话再临OL",
	extra_mobilezhi: "始计篇·智",
	extra_mobilexin: "始计篇·信",
	extra_mobileren: "始计篇·仁",
	extra_offline: "神话再临·线下",
	extra_decade: "神·武",
	extra_tw: "海外服神将",
	extra_mb: "移动版神将",
	extra_hanmo: "风云志·汉末风云",
	extra_changan: "风云志·长安风云",
	extra_taoyuan: "山河煮酒·桃园挽歌",
	extra_jingxiang: "风云志·荆襄风云",
};

export { characterSort, characterSortTranslate };
