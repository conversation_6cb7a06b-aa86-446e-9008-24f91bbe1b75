const characterSort = {
	key_one: ["key_rumi"],
	key_kanon: ["key_shiori", "key_kaori", "key_akiko"],
	key_air: ["key_haruko", "key_yukito", "key_crow", "key_kano", "key_misuzu", "key_minagi", "key_michiru"],
	key_clannad: ["key_yukine", "key_sunohara", "key_tomoya", "key_nagisa", "key_kotomi", "key_fuuko", "key_kyou", "key_tomoyo"],
	key_littlebusters: ["key_kyousuke", "key_komari", "key_masato", "key_kengo", "key_saya", "key_harukakanata", "key_rin", "key_sasami", "key_doruji", "key_yuiko", "key_riki", "key_mio", "key_midori", "key_kud", "key_yuuki"],
	key_rewrite: ["key_lucia", "key_akane", "key_shi<PERSON>ru", "key_kotori", "key_sakuya", "key_chihaya", "key_asara", "key_kagari", "key_kotarou"],
	key_angelbeats: ["sp_key_yuri", "key_yuri", "key_iwasawa", "key_yoshino", "key_yui", "key_shiina", "key_hisako", "key_hinata", "key_noda", "key_ayato", "key_yuzuru", "sp_key_kanade", "key_shiorimiyuki", "key_abyusa", "key_godan"],
	key_charlotte: ["key_yusa", "key_misa", "key_yuu", "key_jojiro", "key_nao"],
	key_harmonia: ["key_rei"],
	key_summerpockets: ["key_umi", "key_umi2", "key_tsumugi", "key_inari", "key_ao", "key_kyoko", "key_miki", "key_ryoichi", "key_shiroha", "key_shizuku", "key_shiki", "key_kamome", "key_tenzen"],
	key_kamisamaninattahi: ["key_hiroto", "key_youta", "db_key_hina", "key_kyouko"],
	key_loopers: ["key_mia"],
	key_lunaria: ["key_iriya", "key_kiyu"],
	key_heavenburnsred: ["db_key_liyingxia", "key_erika", "key_satomi", "key_seira"],
};

const characterSortTranslate = {
	key_one: "ONE ～辉之季节～",
	key_kanon: "Kanon",
	key_air: "AIR",
	key_clannad: "Clannad",
	key_littlebusters: "Little Busters!",
	key_rewrite: "Rewrite",
	key_angelbeats: "Angel Beats!",
	key_charlotte: "Charlotte",
	key_harmonia: "Harmonia",
	key_summerpockets: "Summer Pockets",
	key_kamisamaninattahi: "成神之日",
	key_loopers: "Loopers",
	key_lunaria: "LUNARiA",
	key_heavenburnsred: "炽焰天穹",
};

export { characterSort, characterSortTranslate };
