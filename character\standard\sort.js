const characterSort = {
	standard_2008: ["caocao", "simayi", "xiah<PERSON>un", "zhangliao", "xuzhu", "guojia", "zhenji", "liubei", "guanyu", "zhangfei", "zhugeliang", "zhaoyun", "machao", "huang<PERSON><PERSON>ing", "sunquan", "ganning", "lvmeng", "huanggai", "zhouyu", "daqiao", "luxun", "sunshangxiang", "huatuo", "lvbu", "diaochan"],
	standard_2013: ["old_re_lidian", "huaxiong", "re_yuanshu"],
	standard_2019: ["gongsunzan", "xf_yiji"],
	standard_2023: ["std_panfeng", "ganfuren", "std_yuejin"],
};

const characterSortTranslate = {
	standard_2008: "2008版标准包",
	standard_2013: "2013版标准包",
	standard_2019: "2019版标准包",
	standard_2023: "2023版标准包",
};

export { characterSort, characterSortTranslate };
