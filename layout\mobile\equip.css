#arena:not(.chess).textequip .player[data-position="0"] .equips,
.player:not([data-position="0"]) .equips,
#arena.chess .player .equips {
	width: 120px;
	height: auto;
	top: auto;
	right: auto;
	bottom: 18px;
	left: 10px;
	text-align: left;
}
#arena.slim_player .player:not([data-position="0"]) .equips,
#arena.chess.slim_player .player .equips {
	left: 7px;
}
#arena.chess.lslim_player.slim_player .player .equips {
	left: 5px;
	bottom: 17px;
}
#arena.chess.lslim_player.slim_player .player.minskin.linked .equips {
	transform: rotate(90deg) translate(-98px, -5px) scale(0.73);
}
#arena.uslim_player .player:not([data-position="0"]) .equips,
#arena.chess.uslim_player .player .equips {
	left: 3px;
}
#arena.mslim_player .player:not([data-position="0"]) .equips,
#arena.chess.mslim_player .player .equips {
	left: 5px;
}
.player:not([data-position="0"]).minskin .equips,
#arena.chess .player.minskin .equips {
	transform: scale(0.73);
	transform-origin: bottom left;
}

#arena:not(.chess).textequip .player[data-position="0"] .equips > .card::after,
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card::before,
.player:not([data-position="0"]) .equips > .card::after,
.player:not([data-position="0"]) .equips > .card::before,
#arena.chess .player .equips > .card::after,
#arena.chess .player .equips > .card::before {
	visibility: hidden;
}

#arena:not(.chess).textequip .player[data-position="0"] .equips > .card,
.player:not([data-position="0"]) .equips > .card,
#arena.chess .player .equips > .card {
	position: relative;
	width: 100%;
	height: 22px;
	line-height: 22px;
	margin-top: 0;
	margin-bottom: 0;
	animation: card_start2x 0.5s;
	-webkit-animation: card_start2x 0.5s;
	display: block;
	left: 0;
	top: 0;
	transition: all 0.5s;

	color: white;
	border-radius: 0;
	text-shadow: black 0 0 2px;
}
.player:not([data-position="0"]) .equips > .card,
#arena.chess .player .equips > .card {
	border-width: 1px 0 0;
	border-style: solid;
	border-image: linear-gradient(to right, rgba(0, 0, 0, 0.4) 70%, transparent) 100% 0 0;
	background: linear-gradient(to right, rgba(0, 0, 0, 0.3), transparent),
		linear-gradient(135deg, rgba(0, 0, 0, 0.5), transparent 80%, transparent) !important;
	box-shadow: none;
}
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card > .image,
.player:not([data-position="0"]) .equips > .card > .image,
#arena.chess .player .equips > .card > .image {
	display: none;
}

/*#arena:not(.chess).textequip .player[data-position='0'].unseen .equips>.card,
.player:not([data-position='0']).unseen .equips>.card,
#arena.chess .player.unseen .equips>.card{
	background:none;
	border-image:linear-gradient(to right, transparent,rgba(0,0,0,0.4) 10%,rgba(0, 0, 0,0.4) 70%,transparent) 100% 0 0;
}
#arena:not(.chess).textequip .player[data-position='0'].unseen .equips>.card,
.player:not([data-position='0']).unseen .equips>.card,
#arena.chess .player.unseen .equips>.card{
	border-image:linear-gradient(to right, transparent,rgba(0,0,0,0.4) 10%,rgba(0, 0, 0,0.4) 70%,transparent) 100% 0 0;
}
#arena:not(.chess).textequip .player[data-position='0'].unseen .equips>.card:first-child,
.player:not([data-position='0']).unseen .equips>.card:first-child,
#arena.chess .player.unseen .equips>.card:first-child{
	border-image:linear-gradient(transparent,transparent);
}*/
.player:not([data-position="0"]) .equips > .card.selected,
#arena.chess .player .equips > .card.selected {
	background: linear-gradient(to right, rgba(0, 133, 255, 0.3), transparent),
		linear-gradient(135deg, rgba(0, 133, 255, 0.5), transparent 80%, transparent) !important;
	box-shadow: none !important;
	border-width: 1px 0 0;
	border-style: solid;
	border-image: linear-gradient(to right, rgba(0, 103, 205, 0.4) 70%, transparent) 100% 0 0;
}
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card.selected {
	background: linear-gradient(rgba(0, 133, 255, 0.6), rgba(0, 133, 255, 0.5)) !important;
	box-shadow: rgba(0, 0, 0, 0.4) 0 1px 0 0 !important;
}
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card.fire:not(.fakeequip),
.player:not([data-position="0"]) .equips > .card.fire:not(.fakeequip),
#arena.chess .player .equips > .card.fire:not(.fakeequip) {
	color: rgb(255, 119, 63);
}
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card.thunder:not(.fakeequip),
.player:not([data-position="0"]) .equips > .card.thunder:not(.fakeequip),
#arena.chess .player .equips > .card.thunder:not(.fakeequip) {
	color: rgb(117, 186, 255);
}
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card.poison:not(.fakeequip),
.player:not([data-position="0"]) .equips > .card.poison:not(.fakeequip),
#arena.chess .player .equips > .card.poison:not(.fakeequip) {
	color: rgb(104, 221, 127);
}
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card.brown:not(.fakeequip),
.player:not([data-position="0"]) .equips > .card.brown:not(.fakeequip),
#arena.chess .player .equips > .card.brown:not(.fakeequip) {
	color: rgb(195, 161, 223);
}

#arena:not(.chess).textequip .player[data-position="0"] .equips > .card > .background,
.player:not([data-position="0"]) .equips > .card > .background,
#arena.chess .player .equips > .card > .background {
	display: none !important;
}
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card > .name2,
.player:not([data-position="0"]) .equips > .card > .name2,
#arena.chess .player .equips > .card > .name2 {
	display: block;
	margin-left: 5px;
	white-space: nowrap;
}
#arena.oblongcard:not(.chess).textequip .player[data-position="0"] .equips > .card > .name2 {
	line-height: 29px;
}
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card > div,
.player:not([data-position="0"]) .equips > .card > div,
#arena.chess .player .equips > .card > div {
	animation: none !important;
	-webkit-animation: none !important;
}
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card > .name,
#arena:not(.chess).textequip .player[data-position="0"] .equips > .card > .info,
.player:not([data-position="0"]) .equips > .card > .name,
.player:not([data-position="0"]) .equips > .card > .info,
#arena.chess .player .equips > .card > .name,
#arena.chess .player .equips > .card > .info {
	display: none !important;
}

#arena:not(.chess).textequip .player[data-position="0"] .equips > .removing,
.player:not([data-position="0"]) .equips > .removing,
#arena.chess .player .equips > .removing {
	margin-top: -12px !important;
	margin-bottom: -11px !important;
	transform: scale(1);
}

#arena:not(.chess).textequip .player[data-position="0"] .equips > .removing + .removing,
.player:not([data-position="0"]) .equips > .removing + .removing,
#arena.chess .player .equips > .removing + .removing {
	margin-top: -23px !important;
}

#arena:not(.textequip):not(.chess).textequip .player[data-position="0"].linked .equips,
.player:not([data-position="0"]).linked .equips,
#arena.chess .player.linked .equips {
	transform: rotate(90deg) translate(-152px, -6px);
	transform-origin: bottom left;
}
#arena.slim_player .player:not([data-position="0"]).linked .equips,
#arena.chess.slim_player .player.linked .equips {
	transform: rotate(90deg) translate(-155px, -6px);
}
#arena.uslim_player .player:not([data-position="0"]).linked .equips,
#arena.chess.uslim_player .player.linked .equips {
	transform: rotate(90deg) translate(-159px, -6px);
}
#arena.mslim_player .player:not([data-position="0"]).linked .equips,
#arena.chess.mslim_player .player.linked .equips {
	transform: rotate(90deg) translate(-157px, -6px);
}
.player:not([data-position="0"]).minskin.linked .equips,
#arena.chess .player.minskin.linked .equips {
	transform: rotate(90deg) translate(-92px, -6px) scale(0.73);
}
#arena.slim_player .player:not([data-position="0"]).minskin.linked .equips,
#arena.chess.slim_player .player.minskin.linked .equips {
	transform: rotate(90deg) translate(-95px, -6px) scale(0.73);
}
#arena.uslim_player .player:not([data-position="0"]).minskin.linked .equips,
#arena.chess.uslim_player .player.minskin.linked .equips {
	transform: rotate(90deg) translate(-99px, -6px) scale(0.73);
}
#arena.mslim_player .player:not([data-position="0"]).minskin.linked .equips,
#arena.chess.mslim_player .player.minskin.linked .equips {
	transform: rotate(90deg) translate(-97px, -6px) scale(0.73);
}
.player:not([data-position="0"]).linked .identity,
#arena.chess .player.linked .identity {
	transform: rotate(90deg);
}

#arena:not(.chess).textequip .player[data-position="0"] .equips > .card {
	border-radius: 0px !important;
	left: 0 !important;
	right: auto !important;
	top: 0 !important;
	bottom: auto !important;
	position: relative !important;
	height: 20% !important;

	background: none !important;
	box-shadow: rgba(0, 0, 0, 0.4) 0 1px 0 0;
}
