// 暗身份双将3V3模式身份显示机制测试
console.log("=== 暗身份双将3V3模式身份显示机制测试 ===");

// 模拟游戏状态
const gameState = {
    mode: "hidden_double_3v3",
    players: [],
    me: null
};

// 创建6个玩家
const identities = ["zhu", "zhong", "zhong", "nei", "fan", "fan"];
for (let i = 0; i < 6; i++) {
    const player = {
        id: i,
        name: `Player${i}`,
        identity: identities[i],
        identityShown: false,
        hp: 4,
        maxHp: 4,
        isMe: i === 0, // Player0是玩家自己
        storage: {},
        node: {
            identity: {
                firstChild: { innerHTML: "？" },
                classList: {
                    add: function(cls) { console.log(`身份框添加样式: ${cls}`); },
                    remove: function(cls) { console.log(`身份框移除样式: ${cls}`); }
                }
            }
        },
        setIdentity: function(identity) {
            if (identity) {
                console.log(`${this.name} 身份显示设置为: ${identity}`);
                this.node.identity.firstChild.innerHTML = getIdentityDisplay(identity);
            } else {
                console.log(`${this.name} 身份显示设置为未知`);
                this.node.identity.firstChild.innerHTML = "？";
            }
        },
        takeDamage: function(num, source) {
            this.hp -= num;
            console.log(`${this.name} 受到 ${num} 点伤害，当前血量: ${this.hp}`);
            
            // 触发血量变为1时的身份查看机制
            if (this.hp === 1 && source && source !== this && !this.storage.hidden_identity_revealed) {
                revealIdentity(this, source);
            }
        }
    };
    
    // 不需要设置player引用，直接使用闭包
    
    gameState.players.push(player);
    if (player.isMe) {
        gameState.me = player;
    }
}

// 身份显示函数
function getIdentityDisplay(identity) {
    const identityNames = {
        zhu: "主",
        zhong: "忠",
        nei: "内",
        fan: "反"
    };
    return identityNames[identity] || "？";
}

// 身份揭示函数
function revealIdentity(target, source) {
    console.log(`\n🔍 身份查看触发！`);
    console.log(`${source.name} 将 ${target.name} 的血量降至1点`);
    
    // 标记身份已被揭示
    target.storage.hidden_identity_revealed = true;
    target.identityShown = true;
    
    // 更新身份显示
    target.setIdentity(target.identity);
    target.node.identity.classList.remove("guessing");
    
    // 在日志中显示身份信息
    const identityName = getIdentityName(target.identity);
    console.log(`📋 游戏日志: ${source.name} 将 ${target.name} 的血量降至1点，查看了其身份：${identityName}`);
    console.log(`✅ ${target.name} 的身份已对所有玩家公开显示`);
}

// 获取身份名称
function getIdentityName(identity) {
    const identityNames = {
        zhu: "主公",
        zhong: "忠臣", 
        nei: "内奸",
        fan: "反贼"
    };
    return identityNames[identity] || "未知";
}

// 显示游戏初始状态
console.log("\n=== 游戏初始状态 ===");
console.log("身份分配:");
gameState.players.forEach(player => {
    console.log(`${player.name}: ${player.identity} (${getIdentityName(player.identity)})`);
});

console.log("\n身份显示状态:");
gameState.players.forEach(player => {
    if (player.isMe) {
        console.log(`${player.name}: 可以看到自己的身份 - ${getIdentityDisplay(player.identity)}`);
        player.setIdentity(player.identity);
    } else {
        console.log(`${player.name}: 身份未知 - ？`);
        player.setIdentity();
    }
});

// 测试身份查看机制
console.log("\n=== 测试身份查看机制 ===");

console.log("\n场景1: Player0 攻击 Player1，使其血量降至1");
gameState.players[1].takeDamage(3, gameState.players[0]);

console.log("\n场景2: Player2 攻击 Player3，使其血量降至1");
gameState.players[3].takeDamage(3, gameState.players[2]);

console.log("\n场景3: Player4 攻击已揭示身份的Player1（不会重复揭示）");
if (!gameState.players[1].storage.hidden_identity_revealed) {
    gameState.players[1].takeDamage(1, gameState.players[4]);
} else {
    console.log(`${gameState.players[1].name} 的身份已经被揭示，不会重复触发`);
}

// 显示最终状态
console.log("\n=== 最终身份显示状态 ===");
gameState.players.forEach(player => {
    const status = player.identityShown ? "已揭示" : player.isMe ? "自己可见" : "隐藏";
    const display = player.identityShown || player.isMe ? getIdentityDisplay(player.identity) : "？";
    console.log(`${player.name}: ${display} (${status})`);
});

console.log("\n=== 测试结果 ===");
console.log("✅ 游戏开始时只有自己能看到自己的身份");
console.log("✅ 其他玩家身份显示为'？'，无法点击猜测");
console.log("✅ 血量降至1时正确触发身份查看");
console.log("✅ 身份在日志和身份框中正确显示");
console.log("✅ 已揭示的身份不会重复触发查看机制");

console.log("\n=== 功能验证完成 ===");
